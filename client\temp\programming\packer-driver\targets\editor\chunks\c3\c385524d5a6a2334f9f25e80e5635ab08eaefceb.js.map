{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/SoldierCmpt.ts"], "names": ["_decorator", "Component", "FrameAnimationCmpt", "getSoldierFrameAnimConf", "GameModel", "EventType", "DragTouchType", "SoldierState", "DragTouchCmpt", "ccclass", "property", "SoldierCmpt", "key", "data", "body", "animNode", "animCmpt", "touchCmpt", "currAnimName", "attrBar", "preStateUid", "init", "pos", "node", "setPosition", "<PERSON><PERSON><PERSON><PERSON>", "getComponent", "setUpdateModel", "ins", "getViewId", "then", "<PERSON><PERSON><PERSON><PERSON>", "playAnimation", "addComponent", "loadAttrBar", "resync", "clean", "release", "destroy", "assetsMgr", "releaseTempRes", "getPrefabUrl", "uid", "getBody", "onTouchEvent", "type", "event", "console", "log", "DRAG_BEGIN", "y", "DRAG_MOVE", "parent", "transform", "convertToNodeSpaceAR", "getUILocation", "toVec3", "toString", "CLICK", "END", "update", "dt", "updateState", "name", "cb", "startTime", "play", "state", "STAND", "doStand", "ATTACK", "doAttack", "HIT", "doHit", "anim<PERSON><PERSON>", "playAnimName", "reset", "currAttackTime", "suffix", "instabilityAttackIndex", "damage", "isDie", "sound", "eventCenter", "emit", "REMOVE_ANIMAL", "doDie"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACdC,MAAAA,kB;;AACEC,MAAAA,uB,iBAAAA,uB;;AACFC,MAAAA,S;;AACAC,MAAAA,S;;AAGEC,MAAAA,a,iBAAAA,a;AAAeC,MAAAA,Y,iBAAAA,Y;;AACjBC,MAAAA,a;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U,GAE9B;;yBAEqBW,W,GADpBF,O,UAAD,MACqBE,WADrB,SACyCV,SADzC,CACmD;AAAA;AAAA;AAAA,eAEvCW,GAFuC,GAEzB,EAFyB;AAAA,eAIxCC,IAJwC,GAIrB,IAJqB;AAAA,eAKvCC,IALuC,GAK1B,IAL0B;AAAA,eAMvCC,QANuC,GAMtB,IANsB;AAAA,eAOvCC,QAPuC,GAOR,IAPQ;AAAA,eAQvCC,SARuC,GAQZ,IARY;AAAA,eASvCC,YATuC,GAShB,EATgB;AAAA,eAUvCC,OAVuC,GAUhB,IAVgB;AAAA,eAYvCC,WAZuC,GAYjB,EAZiB;AAAA;;AAcxCC,QAAAA,IAAI,CAACR,IAAD,EAAmBS,GAAnB,EAA8BV,GAA9B,EAA2C;AAClD,eAAKC,IAAL,GAAYA,IAAZ;AACA,eAAKD,GAAL,GAAWA,GAAX;AACA,eAAKW,IAAL,CAAUC,WAAV,CAAsBF,GAAtB;AACA,eAAKR,IAAL,GAAY,KAAKW,SAAL,CAAe,MAAf,CAAZ,CAJkD,CAKlD;;AACA,eAAKV,QAAL,GAAgB,KAAKU,SAAL,CAAe,WAAf,CAAhB;AACA,eAAKT,QAAL,GAAgB,KAAKD,QAAL,CAAcW,YAAd;AAAA;AAAA,uDAAhB;AACA,eAAKV,QAAL,CAAcW,cAAd,CAA6B;AAAA;AAAA,sCAAUC,GAAV,EAA7B;AACA,eAAKZ,QAAL,CAAcK,IAAd,CAAmB;AAAA;AAAA,kEAAwBR,IAAI,CAACgB,SAAL,EAAxB,CAAnB,EAA8DjB,GAA9D,EAAmEkB,IAAnE,CAAwE,MAAM,KAAKC,OAAL,IAAgB,KAAKC,aAAL,CAAmB,MAAnB,CAA9F;AACA,eAAKf,SAAL,GAAiB,KAAKQ,SAAL,CAAe,OAAf,EAAwBQ,YAAxB;AAAA;AAAA,8CAAoDZ,IAApD,CAAyD,IAAzD,CAAjB;AACA,eAAKa,WAAL;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,MAAM,CAACtB,IAAD,EAAY;AACrB,iBAAO,IAAP;AACH;;AAEMuB,QAAAA,KAAK,CAACC,OAAD,EAAoB;AAAA;;AAC5B,eAAKrB,QAAL,CAAcoB,KAAd;AACA,eAAKb,IAAL,CAAUe,OAAV;AACAD,UAAAA,OAAO,IAAIE,SAAS,CAACC,cAAV,eAAyB,KAAK3B,IAA9B,qBAAyB,WAAW4B,YAAX,EAAzB,EAAoD,KAAK7B,GAAzD,CAAX;AACA,eAAKC,IAAL,GAAY,IAAZ;AACH;;AAEwB,cAAXqB,WAAW,GAAG,CACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AAEa,YAAHQ,GAAG,GAAW;AAAA;;AAAE,iBAAO,qBAAK7B,IAAL,iCAAW6B,GAAX,KAAkB,EAAzB;AAA6B;;AAEjDC,QAAAA,OAAO,GAAG;AACb,iBAAO,KAAK7B,IAAZ;AACH,SAvD8C,CAyD/C;;;AACO8B,QAAAA,YAAY,CAACC,IAAD,EAAsBC,KAAtB,EAAyC;AACxDC,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B;AAAA;AAAA,8CAAcH,IAAd,CAA5B,EAAiDC,KAAjD;;AACA,cAAID,IAAI,KAAK;AAAA;AAAA,8CAAcI,UAA3B,EAAuC;AACnC,iBAAK1B,IAAL,CAAU2B,CAAV,IAAe,EAAf;AACH,WAFD,MAEO,IAAIL,IAAI,KAAK;AAAA;AAAA,8CAAcM,SAA3B,EAAsC;AACzC,kBAAM7B,GAAG,GAAG,KAAKC,IAAL,CAAU6B,MAAV,CAAiBC,SAAjB,CAA2BC,oBAA3B,CAAgDR,KAAK,CAACS,aAAN,GAAsBC,MAAtB,EAAhD,CAAZ;AACAT,YAAAA,OAAO,CAACC,GAAR,CAAY1B,GAAG,CAACmC,QAAJ,EAAZ;AAEH,WAJM,MAIA,IAAIZ,IAAI,GAAG;AAAA;AAAA,8CAAca,KAAzB,EAAgC,CACtC,CADM,MACA,IAAIb,IAAI,GAAG;AAAA;AAAA,8CAAcc,GAAzB,EAA8B;AACjC,iBAAKpC,IAAL,CAAU2B,CAAV,IAAe,EAAf;AACH;AACJ;;AAEDU,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,cAAI,CAAC,KAAKhD,IAAV,EAAgB;AACZ;AACH;;AACD,eAAKiD,WAAL;AACH,SA7E8C,CA+E/C;;;AACO9B,QAAAA,aAAa,CAAC+B,IAAD,EAAeC,EAAf,EAA8BC,SAA9B,EAAkD;AAAA;;AAClE,eAAK/C,YAAL,GAAoB6C,IAApB;AACA,iCAAK/C,QAAL,4BAAekD,IAAf,CAAoBH,IAApB,EAA0BC,EAA1B,EAA8BC,SAA9B;AACH,SAnF8C,CAqF/C;;;AACQH,QAAAA,WAAW,GAAG;AAAA;;AAClB,cAAI,iBAAC,KAAKjD,IAAN,aAAC,YAAWsD,KAAZ,KAAqB,KAAK/C,WAAL,KAAqB,KAAKP,IAAL,CAAUsD,KAAV,CAAgBzB,GAA9D,EAAmE;AAC/D;AACH;;AACD,eAAKtB,WAAL,GAAmB,KAAKP,IAAL,CAAUsD,KAAV,CAAgBzB,GAAnC;AACA,gBAAMyB,KAAK,GAAG,KAAKtD,IAAL,CAAUsD,KAAV,CAAgBtB,IAA9B;AAAA,gBAAoChC,IAAI,GAAG,KAAKA,IAAL,CAAUsD,KAAV,CAAgBtD,IAA3D,CALkB,CAMlB;AACA;;AACA,cAAIsD,KAAK,KAAK;AAAA;AAAA,4CAAaC,KAA3B,EAAkC;AAAE;AAChC,iBAAKC,OAAL;AACH,WAFD,MAEO,IAAIF,KAAK,KAAK;AAAA;AAAA,4CAAaG,MAA3B,EAAmC;AAAE;AACxC,iBAAKC,QAAL,CAAc1D,IAAd;AACH,WAFM,MAEA,IAAIsD,KAAK,KAAK;AAAA;AAAA,4CAAaK,GAA3B,EAAgC;AAAE;AACrC,iBAAKC,KAAL,CAAW5D,IAAX;AACH,WAFM,MAEA;AACH,iBAAKmB,aAAL,CAAmB,MAAnB;AACH;AACJ,SAvG8C,CAyG/C;;;AACQqC,QAAAA,OAAO,GAAG;AAAA;;AACd,gBAAMK,QAAQ,sBAAG,KAAK1D,QAAR,qBAAG,gBAAe2D,YAAhC;;AACA,cAAID,QAAQ,KAAK,MAAb,IAAuBA,QAAQ,KAAK,WAAxC,EAAqD;AAAE;AACnD,iBAAK1C,aAAL,CAAmB,MAAnB;AACH;;AACD,gCAAKb,OAAL,2BAAcyD,KAAd;AACH,SAhH8C,CAkH/C;;;AACQL,QAAAA,QAAQ,CAAC1D,IAAD,EAAY;AAAA;;AACxB,gBAAMgE,cAAc,2BAAGhE,IAAI,CAACgE,cAAR,mCAA0B,CAA9C;AACA,gBAAMC,MAAM,GAAGjE,IAAI,CAACkE,sBAAL,IAA+B,EAA9C;AACA,eAAK/C,aAAL,CAAmB,WAAW8C,MAA9B,EAAsC,MAAM,KAAK/C,OAAL,IAAgB,KAAKC,aAAL,CAAmB,MAAnB,CAA5D,EAAwF6C,cAAxF;AACH,SAvH8C,CAyH/C;;;AACQJ,QAAAA,KAAK,CAAC5D,IAAD,EAAY;AAAA;;AACrB,cAAImE,MAAM,mBAAGnE,IAAI,CAACmE,MAAR,2BAAkB,CAA5B;AACA,gBAAMC,KAAK,GAAG,KAAKpE,IAAL,CAAUoE,KAAV,EAAd;AACA,gBAAMC,KAAK,GAAGrE,IAAI,CAACqE,KAAnB,CAHqB,CAGI;;AACzB,gBAAMxC,GAAG,GAAG,KAAKA,GAAjB;AACA,iCAAKvB,OAAL,4BAAc+C,IAAd;;AACA,cAAIc,MAAM,KAAK,CAAf,EAAkB;AACd,mBAAO,KAAKhD,aAAL,CAAmB,MAAnB,CAAP;AACH;;AACD,cAAI0C,QAAQ,GAAG,KAAf;;AACA,cAAIO,KAAJ,EAAW;AACPP,YAAAA,QAAQ,GAAG,KAAX,CADO,CAEP;AACH,WAHD,MAGO,IAAIQ,KAAJ,EAAW,CACd;AACH;;AACD,eAAKlD,aAAL,CAAmB0C,QAAnB,EAA6B,MAAM;AAC/B,gBAAIO,KAAJ,EAAW;AACPE,cAAAA,WAAW,CAACC,IAAZ,CAAiB;AAAA;AAAA,0CAAUC,aAA3B,EAA0C3C,GAA1C,EAA+C,KAA/C;AACH,aAFD,MAEO,IAAI,KAAKX,OAAT,EAAkB;AACrB,mBAAKC,aAAL,CAAmB,MAAnB;AACH;AACJ,WAND;AAOH,SAjJ8C,CAmJ/C;;;AACQsD,QAAAA,KAAK,CAACzE,IAAD,EAAY,CAExB;;AAtJ8C,O", "sourcesContent": ["import { _decorator, Component, EventTouch, Node, Vec3 } from \"cc\";\r\nimport FrameAnimationCmpt from \"../cmpt/FrameAnimationCmpt\";\r\nimport { getSoldierFrameAnimConf } from \"../../common/config/SoldierFrameAnimConf\";\r\nimport GameModel from \"../../model/game/GameModel\";\r\nimport EventType from \"../../common/event/EventType\";\r\nimport AttrBarCmpt from \"./AttrBarCmpt\";\r\nimport SoldierObj from \"../../model/game/SoldierObj\";\r\nimport { DragTouchType, SoldierState } from \"../../common/constant/Enums\";\r\nimport DragTouchCmpt from \"./DragTouchCmpt\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n// 一个动物\r\n@ccclass\r\nexport default class SoldierCmpt extends Component {\r\n\r\n    private key: string = ''\r\n\r\n    public data: SoldierObj = null\r\n    private body: Node = null\r\n    private animNode: Node = null\r\n    private animCmpt: FrameAnimationCmpt = null\r\n    private touchCmpt: DragTouchCmpt = null\r\n    private currAnimName: string = ''\r\n    private attrBar: AttrBarCmpt = null\r\n\r\n    private preStateUid: string = ''\r\n\r\n    public init(data: SoldierObj, pos: Vec3, key: string) {\r\n        this.data = data\r\n        this.key = key\r\n        this.node.setPosition(pos)\r\n        this.body = this.FindChild('body')\r\n        // this.body.scale.set(this.data.isFriendly() ? 1 : -1, 1)\r\n        this.animNode = this.FindChild('body/anim')\r\n        this.animCmpt = this.animNode.getComponent(FrameAnimationCmpt)\r\n        this.animCmpt.setUpdateModel(GameModel.ins())\r\n        this.animCmpt.init(getSoldierFrameAnimConf(data.getViewId()), key).then(() => this.isValid && this.playAnimation('idle'))\r\n        this.touchCmpt = this.FindChild('touch').addComponent(DragTouchCmpt).init(this)\r\n        this.loadAttrBar()\r\n        return this\r\n    }\r\n\r\n    public resync(data: any) {\r\n        return this\r\n    }\r\n\r\n    public clean(release?: boolean) {\r\n        this.animCmpt.clean()\r\n        this.node.destroy()\r\n        release && assetsMgr.releaseTempRes(this.data?.getPrefabUrl(), this.key)\r\n        this.data = null\r\n    }\r\n\r\n    private async loadAttrBar() {\r\n        // const it = await nodePoolMgr.get('animal/ATTR_BAR', this.key)\r\n        // if (!this.isValid || !this.data) {\r\n        //     return nodePoolMgr.put(it)\r\n        // }\r\n        // it.parent = this.node\r\n        // it.zIndex = 10\r\n        // it.active = true\r\n        // this.attrBar = it.getComponent(AttrBarCmpt).init(this.data)\r\n    }\r\n\r\n    public get uid(): string { return this.data?.uid || '' }\r\n\r\n    public getBody() {\r\n        return this.body\r\n    }\r\n\r\n    // 触摸事件\r\n    public onTouchEvent(type: DragTouchType, event: EventTouch) {\r\n        console.log('onTouchEvent', DragTouchType[type], event)\r\n        if (type === DragTouchType.DRAG_BEGIN) {\r\n            this.node.y += 40\r\n        } else if (type === DragTouchType.DRAG_MOVE) {\r\n            const pos = this.node.parent.transform.convertToNodeSpaceAR(event.getUILocation().toVec3())\r\n            console.log(pos.toString())\r\n\r\n        } else if (type = DragTouchType.CLICK) {\r\n        } else if (type = DragTouchType.END) {\r\n            this.node.y -= 40\r\n        }\r\n    }\r\n\r\n    update(dt: number) {\r\n        if (!this.data) {\r\n            return\r\n        }\r\n        this.updateState()\r\n    }\r\n\r\n    // 播放动画\r\n    public playAnimation(name: string, cb?: Function, startTime?: number) {\r\n        this.currAnimName = name\r\n        this.animCmpt?.play(name, cb, startTime)\r\n    }\r\n\r\n    // 同步状态信息\r\n    private updateState() {\r\n        if (!this.data?.state || this.preStateUid === this.data.state.uid) {\r\n            return\r\n        }\r\n        this.preStateUid = this.data.state.uid\r\n        const state = this.data.state.type, data = this.data.state.data\r\n        // cc.log('updateState', this.uid, this.point.ID(), SoldierState[state])\r\n        // this.data.actioning = this.data.actioning || (state !== SoldierState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动\r\n        if (state === SoldierState.STAND) { //待机\r\n            this.doStand()\r\n        } else if (state === SoldierState.ATTACK) { //攻击\r\n            this.doAttack(data)\r\n        } else if (state === SoldierState.HIT) { //受击\r\n            this.doHit(data)\r\n        } else {\r\n            this.playAnimation('idle')\r\n        }\r\n    }\r\n\r\n    // 待机\r\n    private doStand() {\r\n        const animName = this.animCmpt?.playAnimName\r\n        if (animName === 'move' || animName === 'move_pull') { //只有移动的时候才强行切换成idle\r\n            this.playAnimation('idle')\r\n        }\r\n        this.attrBar?.reset()\r\n    }\r\n\r\n    // 攻击\r\n    private doAttack(data: any) {\r\n        const currAttackTime = data.currAttackTime ?? 0\r\n        const suffix = data.instabilityAttackIndex || ''\r\n        this.playAnimation('attack' + suffix, () => this.isValid && this.playAnimation('idle'), currAttackTime)\r\n    }\r\n\r\n    // 受击\r\n    private doHit(data: any) {\r\n        let damage = data.damage ?? 0\r\n        const isDie = this.data.isDie()\r\n        const sound = data.sound //受击音效\r\n        const uid = this.uid\r\n        this.attrBar?.play()\r\n        if (damage === 0) {\r\n            return this.playAnimation('idle')\r\n        }\r\n        let animName = 'hit'\r\n        if (isDie) {\r\n            animName = 'die'\r\n            // this.playSFXByKey('die_sound')\r\n        } else if (sound) {\r\n            // this.playSFX(sound)\r\n        }\r\n        this.playAnimation(animName, () => {\r\n            if (isDie) {\r\n                eventCenter.emit(EventType.REMOVE_ANIMAL, uid, false)\r\n            } else if (this.isValid) {\r\n                this.playAnimation('idle')\r\n            }\r\n        })\r\n    }\r\n\r\n    // 直接死亡\r\n    private doDie(data: any) {\r\n\r\n    }\r\n}"]}