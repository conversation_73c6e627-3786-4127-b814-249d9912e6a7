import { _decorator, Component, EventTouch, Node, v2, Vec2 } from "cc";
import { CLICK_SPACE } from "../../common/constant/Constant";
import { IDragTarget } from "../../common/constant/interface";
import { gHelper } from "../../common/helper/GameHelper";
import { DragTouchType } from "../../common/constant/Enums";

const { ccclass, property } = _decorator;

/**
 * 用于拖动的组建
 */
@ccclass
export default class DragTouchCmpt extends Component {

    public interactable: boolean = true

    private target: IDragTarget = null
    private isDownClick: boolean = false //是否按下点击
    private isFirstDrag: boolean = false //是否首次拖动

    private _temp_vec2_1: Vec2 = v2()
    private _temp_vec2_2: Vec2 = v2()

    public init(target: IDragTarget) {
        this.target = target
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this)
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this)
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this)
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this)
        this.node.SetSwallowTouches(false) //默认开启穿透
        return this
    }

    public clean() {
        this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this)
        this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMove, this)
        this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this)
        this.node.off(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this)
        this.target = null
    }

    // 触摸开始
    private onTouchStart(event: EventTouch) {
        if (!this.interactable || gHelper.clickTouchId !== -1) {
            return
        }
        gHelper.clickTouchId = event.getID()
        this.isDownClick = true
        this.isFirstDrag = false
    }

    private onTouchMove(event: EventTouch) {
        if (!this.interactable || gHelper.clickTouchId !== event.getID()) {
            return
        }
        const startLocation = event.getStartLocation(this._temp_vec2_1)
        const location = event.getLocation(this._temp_vec2_2)
        const mag = startLocation.subtract(location).length()
        if (mag > 4) {
            this.isDownClick = false
            if (!this.isFirstDrag) {
                this.isFirstDrag = true
                this.target.onTouchEvent(DragTouchType.DRAG_BEGIN, event)
            }
            this.target.onTouchEvent(DragTouchType.DRAG_MOVE, event)
        }
    }

    private onTouchEnd(event: EventTouch) {
        if (gHelper.clickTouchId !== event.getID() || !this.interactable) {
            return
        }
        gHelper.clickTouchId = -1
        if (this.isDownClick) {
            const startLocation = event.getStartLocation(this._temp_vec2_1)
            const location = event.getLocation(this._temp_vec2_2)
            const mag = startLocation.subtract(location).length()
            if (mag <= CLICK_SPACE) {
                this.target.onTouchEvent(DragTouchType.CLICK, event)
            }
        }
        this.target.onTouchEvent(DragTouchType.END, event)
    }
}