{"version": 3, "sources": ["file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts"], "names": ["PipelineConfigs", "CameraConfigs", "ForwardLighting", "BuiltinForwardPassBuilder", "BuiltinBloomPassBuilder", "BuiltinToneMappingPassBuilder", "BuiltinFXAAPassBuilder", "BuiltinFsrPassBuilder", "BuiltinUiPassBuilder", "forwardNeedClearColor", "camera", "clearFlag", "ClearFlagBit", "COLOR", "STENCIL", "getCsmMainLightViewport", "light", "w", "h", "level", "vp", "screenSpaceSignY", "shadowFixedArea", "csmLevel", "CSMLevel", "LEVEL_1", "left", "top", "width", "Math", "trunc", "height", "floor", "max", "setupPipelineConfigs", "ppl", "configs", "sampleFeature", "FormatFeatureBit", "SAMPLED_TEXTURE", "LINEAR_FILTER", "device", "isWeb", "sys", "isNative", "isWebGL1", "gfxAPI", "gfx", "API", "WEBGL", "isWebGPU", "WEBGPU", "isMobile", "isHDR", "pipelineSceneData", "useFloatOutput", "getMacroBool", "toneMappingType", "postSettings", "shadowInfo", "shadows", "shadowEnabled", "enabled", "shadowMapFormat", "pipeline", "supportsR32FloatTexture", "Format", "R32F", "RGBA8", "shadowMapSize", "set", "size", "usePlanarShadow", "type", "renderer", "scene", "ShadowType", "Planar", "capabilities", "supportDepthSample", "getFormatFeatures", "DEPTH_STENCIL", "platform", "x", "clipSpaceSignY", "sortPipelinePassBuildersByConfigOrder", "passBuilders", "sort", "a", "b", "getConfigOrder", "sortPipelinePassBuildersByRenderOrder", "getRenderOrder", "addCopyToScreenPass", "pplConfigs", "cameraConfigs", "input", "assert", "copyAndTonemapMaterial", "pass", "addRenderPass", "nativeWidth", "nativeHeight", "addR<PERSON><PERSON>arget", "colorName", "LoadOp", "CLEAR", "StoreOp", "STORE", "sClearColorTransparentBlack", "addTexture", "addQueue", "rendering", "QueueHint", "OPAQUE", "addFullscreenQuad", "getPingPongRenderTarget", "prevName", "prefix", "id", "startsWith", "Number", "char<PERSON>t", "length", "downSize", "scale", "cclegacy", "clamp", "geometry", "Layers", "Material", "PipelineEventType", "Vec2", "Vec3", "Vec4", "warn", "DEBUG", "EDITOR", "BloomType", "makePipelineSettings", "AABB", "Sphere", "intersect", "Color", "TextureType", "Viewport", "CameraUsage", "LightType", "mobileMaxSpotLightShadowMaps", "defaultSettings", "settings", "isMainGameWindow", "renderWindowId", "depthStencilName", "enableFullPipeline", "enableProfiler", "remainingPasses", "enableShadingScale", "shadingScale", "enableHDR", "radianceFormat", "enableStoreSceneDepth", "lights", "shadowEnabledSpotLights", "_sphere", "create", "_boundingBox", "_rangedDirLightBoundingBox", "cullLights", "frustum", "cameraPos", "spotLights", "baked", "position", "y", "z", "range", "sphereFrustum", "push", "sphereLights", "pointLights", "rangedDirLights", "transform", "node", "getWorldMatrix", "aabbFrustum", "lhs", "rhs", "squaredDistance", "_addLightQueues", "queue", "BLEND", "SPHERE", "name", "SPOT", "POINT", "RANGED_DIRECTIONAL", "addScene", "SceneFlags", "addSpotlightShadowPasses", "maxNumShadowMaps", "i", "<PERSON><PERSON><PERSON>", "addDepthStencil", "DISCARD", "NONE", "MASK", "SHADOW_CASTER", "useLightFrustum", "addLightQueues", "addLightPasses", "depthStencilStoreOp", "viewport", "count", "storeOp", "setViewport", "LOAD", "isMultipleLightPassesNeeded", "forwardLighting", "_viewport", "_clearColor", "_reflectionProbeClearColor", "ConfigOrder", "RenderOrder", "configCamera", "pipelineConfigs", "enableMainLightShadowMap", "mainLight", "enableMainLightPlanarShadowMap", "enablePlanarReflectionProbe", "cameraUsage", "SCENE_VIEW", "GAME_VIEW", "enableMSAA", "msaa", "enableSingleForwardPass", "windowResize", "window", "ResourceFlags", "ResourceResidency", "TEX2D", "sampleCount", "COLOR_ATTACHMENT", "MEMORYLESS", "DEPTH_STENCIL_ATTACHMENT", "setup", "context", "setVec4", "_addCascadedShadowMapPass", "_tryAddReflectionProbePasses", "_addForwardRadiancePasses", "shadowSize", "csmSupported", "reflectionProbeManager", "internal", "probes", "getProbes", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "probeID", "probe", "needRender", "area", "renderArea", "probeType", "ProbeType", "PLANAR", "realtimePlanarTexture", "addRenderWindow", "probePass", "_buildReflectionProbePass", "faceIdx", "bakedCubeTextures", "updateCameraDir", "colorStoreOp", "clearColor", "packRGBE", "clear<PERSON><PERSON>h", "clearStencil", "REFLECTION_PROBE", "undefined", "disableMSAA", "round", "_addForwardSingleRadiancePass", "_addForwardMultipleRadiancePasses", "_addPlanarShadowQueue", "sceneFlags", "<PERSON><PERSON><PERSON><PERSON>", "GEOMETRY", "msaaRadianceName", "msaaDepthStencilName", "msPass", "addMultisampleRenderPass", "_buildForwardMainLightPass", "resolve<PERSON><PERSON><PERSON><PERSON><PERSON>", "firstStoreOp", "PLANAR_SHADOW", "_clearColorTransparentBlack", "_bloomParams", "_bloomTexSize", "_bloomWidths", "_bloomHeights", "_bloomTexNames", "_bloomUpSampleTexDescs", "_bloomDownSampleTexDescs", "_prefilterTexDesc", "_originalColorDesc", "bloom", "hasValidMaterial", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kawaseFilterMaterial", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mipmapFilterMaterial", "enableBloom", "format", "bloomWidth", "bloomHeight", "iterations", "pow", "createTexture", "desc", "prevRenderPass", "material", "_addKawaseDualFilterBloomPasses", "_addMipmapFilterBloomPasses", "bloomMaterial", "radianceName", "sizeCount", "threshold", "enableAlphaMask", "prefilterPass", "downPass", "upPass", "intensity", "combinePass", "_addPass", "layout", "passIndex", "loadOp", "queueHint", "prefilterInfo", "currSamplePass", "downSampleInfos", "currInfo", "samplerSrc", "samplerSrcName", "lastIndex", "upSampleInfos", "sampleSrc", "sampleSrcName", "_colorGradingTexSize", "enableColorGrading", "colorGrading", "colorGradingMap", "enableToneMapping", "setProperty", "_addCopyAndTonemapPass", "ldrColorPrefix", "ldrColorName", "lutTex", "isSquareMap", "setVec2", "setFloat", "contribute", "toneMapping", "_fxaaParams", "enableFXAA", "fxaa", "_addFxaaPass", "inputColorName", "lastPass", "fxaaMaterial", "_fsrParams", "_fsrTexSize", "enableFSR", "fsr", "outputColorName", "_addFsrPass", "fsrMaterial", "sharpness", "uiColorPrefix", "fsrColorName", "easu<PERSON><PERSON>", "rcasPass", "flags", "UI", "PROFILER", "showStatistics", "BuiltinPipelineBuilder", "_pipelineEvent", "director", "root", "pipelineEvent", "_forwardPass", "_bloomPass", "_toneMappingPass", "_fxaaPass", "_fsrPass", "_uiPass", "_configs", "_cameraConfigs", "_copyAndTonemapMaterial", "_initialized", "_passBuilders", "_setupPipelinePreview", "isEditorView", "PREVIEW", "editorSettings", "getEditorPipelineSettings", "pipelineSettings", "_prepare<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_passes", "_setupBuiltinCameraConfigs", "GAME", "swapchain", "isGameView", "visibility", "Enum", "DEFAULT", "profiler", "RGBA16F", "_setupCameraConfigs", "builder", "cameras", "_initMaterials", "emit", "RENDER_CAMERA_BEGIN", "_buildFor<PERSON><PERSON><PERSON><PERSON>e", "_buildSimplePipeline", "RENDER_CAMERA_END", "_uuid", "initialize", "effectName", "effectAsset", "setCustomPipeline"], "mappings": ";;;wQA4EaA,e,EAwDAC,a,EA6EPC,e,EAoMOC,yB,EAilBAC,uB,EAoWAC,6B,EA2HAC,sB,EAoGAC,qB,EAqGAC,oB;;AAnmDb,WAASC,qBAAT,CAA+BC,MAA/B,EAAuE;AACnE,WAAO,CAAC,EAAEA,MAAM,CAACC,SAAP,IAAoBC,YAAY,CAACC,KAAb,GAAsBD,YAAY,CAACE,OAAb,IAAwB,CAAlE,CAAF,CAAR;AACH;;AAED,WAASC,uBAAT,CACIC,KADJ,EAEIC,CAFJ,EAGIC,CAHJ,EAIIC,KAJJ,EAKIC,EALJ,EAMIC,gBANJ,EAOQ;AACJ,QAAIL,KAAK,CAACM,eAAN,IAAyBN,KAAK,CAACO,QAAN,KAAmBC,QAAQ,CAACC,OAAzD,EAAkE;AAC9DL,MAAAA,EAAE,CAACM,IAAH,GAAU,CAAV;AACAN,MAAAA,EAAE,CAACO,GAAH,GAAS,CAAT;AACAP,MAAAA,EAAE,CAACQ,KAAH,GAAWC,IAAI,CAACC,KAAL,CAAWb,CAAX,CAAX;AACAG,MAAAA,EAAE,CAACW,MAAH,GAAYF,IAAI,CAACC,KAAL,CAAWZ,CAAX,CAAZ;AACH,KALD,MAKO;AACHE,MAAAA,EAAE,CAACM,IAAH,GAAUG,IAAI,CAACC,KAAL,CAAWX,KAAK,GAAG,CAAR,GAAY,GAAZ,GAAkBF,CAA7B,CAAV;;AACA,UAAII,gBAAgB,GAAG,CAAvB,EAA0B;AACtBD,QAAAA,EAAE,CAACO,GAAH,GAASE,IAAI,CAACC,KAAL,CAAW,CAAC,IAAID,IAAI,CAACG,KAAL,CAAWb,KAAK,GAAG,CAAnB,CAAL,IAA8B,GAA9B,GAAoCD,CAA/C,CAAT;AACH,OAFD,MAEO;AACHE,QAAAA,EAAE,CAACO,GAAH,GAASE,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACG,KAAL,CAAWb,KAAK,GAAG,CAAnB,IAAwB,GAAxB,GAA8BD,CAAzC,CAAT;AACH;;AACDE,MAAAA,EAAE,CAACQ,KAAH,GAAWC,IAAI,CAACC,KAAL,CAAW,MAAMb,CAAjB,CAAX;AACAG,MAAAA,EAAE,CAACW,MAAH,GAAYF,IAAI,CAACC,KAAL,CAAW,MAAMZ,CAAjB,CAAZ;AACH;;AACDE,IAAAA,EAAE,CAACM,IAAH,GAAUG,IAAI,CAACI,GAAL,CAAS,CAAT,EAAYb,EAAE,CAACM,IAAf,CAAV;AACAN,IAAAA,EAAE,CAACO,GAAH,GAASE,IAAI,CAACI,GAAL,CAAS,CAAT,EAAYb,EAAE,CAACO,GAAf,CAAT;AACAP,IAAAA,EAAE,CAACQ,KAAH,GAAWC,IAAI,CAACI,GAAL,CAAS,CAAT,EAAYb,EAAE,CAACQ,KAAf,CAAX;AACAR,IAAAA,EAAE,CAACW,MAAH,GAAYF,IAAI,CAACI,GAAL,CAAS,CAAT,EAAYb,EAAE,CAACW,MAAf,CAAZ;AACH;;AAqBD,WAASG,oBAAT,CACIC,GADJ,EAEIC,OAFJ,EAGQ;AACJ,UAAMC,aAAa,GAAGC,gBAAgB,CAACC,eAAjB,GAAmCD,gBAAgB,CAACE,aAA1E;AACA,UAAMC,MAAM,GAAGN,GAAG,CAACM,MAAnB,CAFI,CAGJ;;AACAL,IAAAA,OAAO,CAACM,KAAR,GAAgB,CAACC,GAAG,CAACC,QAArB;AACAR,IAAAA,OAAO,CAACS,QAAR,GAAmBJ,MAAM,CAACK,MAAP,KAAkBC,GAAG,CAACC,GAAJ,CAAQC,KAA7C;AACAb,IAAAA,OAAO,CAACc,QAAR,GAAmBT,MAAM,CAACK,MAAP,KAAkBC,GAAG,CAACC,GAAJ,CAAQG,MAA7C;AACAf,IAAAA,OAAO,CAACgB,QAAR,GAAmBT,GAAG,CAACS,QAAvB,CAPI,CASJ;;AACAhB,IAAAA,OAAO,CAACiB,KAAR,GAAgBlB,GAAG,CAACmB,iBAAJ,CAAsBD,KAAtC,CAVI,CAUyC;;AAC7CjB,IAAAA,OAAO,CAACmB,cAAR,GAAyBpB,GAAG,CAACqB,YAAJ,CAAiB,qBAAjB,CAAzB;AACApB,IAAAA,OAAO,CAACqB,eAAR,GAA0BtB,GAAG,CAACmB,iBAAJ,CAAsBI,YAAtB,CAAmCD,eAA7D,CAZI,CAaJ;;AACA,UAAME,UAAU,GAAGxB,GAAG,CAACmB,iBAAJ,CAAsBM,OAAzC;AACAxB,IAAAA,OAAO,CAACyB,aAAR,GAAwBF,UAAU,CAACG,OAAnC;AACA1B,IAAAA,OAAO,CAAC2B,eAAR,GAA0BC,QAAQ,CAACC,uBAAT,CAAiC9B,GAAG,CAACM,MAArC,IAA+CyB,MAAM,CAACC,IAAtD,GAA6DD,MAAM,CAACE,KAA9F;AACAhC,IAAAA,OAAO,CAACiC,aAAR,CAAsBC,GAAtB,CAA0BX,UAAU,CAACY,IAArC;AACAnC,IAAAA,OAAO,CAACoC,eAAR,GAA0Bb,UAAU,CAACG,OAAX,IAAsBH,UAAU,CAACc,IAAX,KAAoBC,QAAQ,CAACC,KAAT,CAAeC,UAAf,CAA0BC,MAA9F,CAlBI,CAmBJ;;AACAzC,IAAAA,OAAO,CAACf,gBAAR,GAA2Bc,GAAG,CAACM,MAAJ,CAAWqC,YAAX,CAAwBzD,gBAAnD;AACAe,IAAAA,OAAO,CAAC2C,kBAAR,GAA6B,CAAC5C,GAAG,CAACM,MAAJ,CAAWuC,iBAAX,CAA6Bd,MAAM,CAACe,aAApC,IAAqD5C,aAAtD,MAAyEA,aAAtG,CArBI,CAsBJ;;AACA,UAAMhB,gBAAgB,GAAGoB,MAAM,CAACqC,YAAP,CAAoBzD,gBAA7C;AACAe,IAAAA,OAAO,CAAC8C,QAAR,CAAiBC,CAAjB,GAAqB/C,OAAO,CAACgB,QAAR,GAAmB,GAAnB,GAAyB,GAA9C;AACAhB,IAAAA,OAAO,CAAC8C,QAAR,CAAiBjE,CAAjB,GAAsBI,gBAAgB,GAAG,GAAnB,GAAyB,GAA1B,IAAkC,CAAlC,GAAuCoB,MAAM,CAACqC,YAAP,CAAoBM,cAApB,GAAqC,GAArC,GAA2C,GAAvG;AACH;;AAuCD,WAASC,qCAAT,CAA+CC,YAA/C,EAAoG;AAChGA,IAAAA,YAAY,CAACC,IAAb,CAAkB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AACxB,aAAOD,CAAC,CAACE,cAAF,KAAqBD,CAAC,CAACC,cAAF,EAA5B;AACH,KAFD;AAGH;;AAED,WAASC,qCAAT,CAA+CL,YAA/C,EAAoG;AAChGA,IAAAA,YAAY,CAACC,IAAb,CAAkB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AACxB,aAAOD,CAAC,CAACI,cAAF,KAAqBH,CAAC,CAACG,cAAF,EAA5B;AACH,KAFD;AAGH;;AAED,WAASC,mBAAT,CACI1D,GADJ,EAEI2D,UAFJ,EAGIC,aAHJ,EAIIC,KAJJ,EAKoC;AAChCC,IAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACG,sBAAjB,CAAN;AACA,UAAMC,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CACTL,aAAa,CAACM,WADL,EAETN,aAAa,CAACO,YAFL,EAGT,iBAHS,CAAb;AAIAH,IAAAA,IAAI,CAACI,eAAL,CACIR,aAAa,CAACS,SADlB,EAEIC,MAAM,CAACC,KAFX,EAEkBC,OAAO,CAACC,KAF1B,EAGIC,2BAHJ;AAIAV,IAAAA,IAAI,CAACW,UAAL,CAAgBd,KAAhB,EAAuB,cAAvB;AACAG,IAAAA,IAAI,CAACY,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoBC,MAAlC,EACKC,iBADL,CACuBpB,aAAa,CAACG,sBADrC,EAC6D,CAD7D;AAEA,WAAOC,IAAP;AACH;;AAEM,WAASiB,uBAAT,CAAiCC,QAAjC,EAAmDC,MAAnD,EAAmEC,EAAnE,EAAuF;AAC1F,QAAIF,QAAQ,CAACG,UAAT,CAAoBF,MAApB,CAAJ,EAAiC;AAC7B,aAAQ,GAAEA,MAAO,GAAE,IAAIG,MAAM,CAACJ,QAAQ,CAACK,MAAT,CAAgBJ,MAAM,CAACK,MAAvB,CAAD,CAAiC,IAAGJ,EAAG,EAApE;AACH,KAFD,MAEO;AACH,aAAQ,GAAED,MAAO,KAAIC,EAAG,EAAxB;AACH;AACJ;;AAkxBD,WAASK,QAAT,CAAkBrD,IAAlB,EAAgCsD,KAAhC,EAAuD;AACnD,WAAOhG,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWuC,IAAI,GAAGsD,KAAlB,CAAT,EAAmC,CAAnC,CAAP;AACH;;;;;;;;;;;;;;;;;6BA1xBeT,uB;;;;;;;;;;;;;;;;AA3KZnB,MAAAA,M,OAAAA,M;AAAQ6B,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;AAAUjF,MAAAA,G,OAAAA,G;AAAKkF,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Q,OAAAA,Q;AAAUlE,MAAAA,Q,OAAAA,Q;AAClCmE,MAAAA,iB,OAAAA,iB;AAA2CzD,MAAAA,Q,OAAAA,Q;AACnEsC,MAAAA,S,OAAAA,S;AAAWrE,MAAAA,G,OAAAA,G;AAAKyF,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAG7BC,MAAAA,K,UAAAA,K;AAAOC,MAAAA,M,UAAAA,M;;AAGZC,MAAAA,S,iBAAAA,S;AACAC,MAAAA,oB,iBAAAA,oB;;;;;;AAlCJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;OAgBM;AAAEC,QAAAA,IAAF;AAAQC,QAAAA,MAAR;AAAgBC,QAAAA;AAAhB,O,GAA8Bd,Q;OAC9B;AAAEpH,QAAAA,YAAF;AAAgBmI,QAAAA,KAAhB;AAAuB7E,QAAAA,MAAvB;AAA+B5B,QAAAA,gBAA/B;AAAiDmE,QAAAA,MAAjD;AAAyDE,QAAAA,OAAzD;AAAkEqC,QAAAA,WAAlE;AAA+EC,QAAAA;AAA/E,O,GAA4FlG,G;OAC5F;AAAE4B,QAAAA;AAAF,O,GAAYD,Q;OACZ;AAAEwE,QAAAA,WAAF;AAAe1H,QAAAA,QAAf;AAAyB2H,QAAAA;AAAzB,O,GAAuCxE,K;;iCAmChC3E,e,GAAN,MAAMA,eAAN,CAAsB;AAAA;AAAA,eACzB0C,KADyB,GACjB,KADiB;AAAA,eAEzBG,QAFyB,GAEd,KAFc;AAAA,eAGzBK,QAHyB,GAGd,KAHc;AAAA,eAIzBE,QAJyB,GAId,KAJc;AAAA,eAKzBC,KALyB,GAKjB,KALiB;AAAA,eAMzBE,cANyB,GAMR,KANQ;AAAA,eAOzBE,eAPyB,GAOP,CAPO;AAOJ;AAPI,eAQzBI,aARyB,GAQT,KARS;AAAA,eASzBE,eATyB,GASPG,MAAM,CAACC,IATA;AAAA,eAUzBE,aAVyB,GAUT,IAAI+D,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAVS;AAAA,eAWzB5D,eAXyB,GAWP,KAXO;AAAA,eAYzBnD,gBAZyB,GAYN,CAZM;AAAA,eAazB0D,kBAbyB,GAaJ,KAbI;AAAA,eAczBqE,4BAdyB,GAcM,CAdN;AAAA,eAgBzBlE,QAhByB,GAgBd,IAAIoD,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAhBc;AAAA;;AAAA,O;;AAsDvBe,MAAAA,e,GAAkB;AAAA;AAAA,yD;;+BAEXpJ,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,eACvBqJ,QADuB,GACMD,eADN;AAEvB;AAFuB,eAGvBE,gBAHuB,GAGJ,KAHI;AAAA,eAIvBC,cAJuB,GAIN,CAJM;AAKvB;AALuB,eAMvBhD,SANuB,GAMX,EANW;AAAA,eAOvBiD,gBAPuB,GAOJ,EAPI;AAQvB;AARuB,eASvBC,kBATuB,GASF,KATE;AAAA,eAUvBC,cAVuB,GAUN,KAVM;AAAA,eAWvBC,eAXuB,GAWL,CAXK;AAYvB;AAZuB,eAavBC,kBAbuB,GAaF,KAbE;AAAA,eAcvBC,YAduB,GAcR,GAdQ;AAAA,eAevBzD,WAfuB,GAeT,CAfS;AAAA,eAgBvBC,YAhBuB,GAgBR,CAhBQ;AAAA,eAiBvB1E,KAjBuB,GAiBf,CAjBe;AAiBZ;AAjBY,eAkBvBG,MAlBuB,GAkBd,CAlBc;AAkBX;AACZ;AAnBuB,eAoBvBgI,SApBuB,GAoBX,KApBW;AAAA,eAqBvBC,cArBuB,GAqBNjH,GAAG,CAACmB,MAAJ,CAAWE,KArBL;AAsBvB;AAtBuB,eAuBvB8B,sBAvBuB,GAuBmB,IAvBnB;AAwBvB;;AACA;AAzBuB,eA0BvB+D,qBA1BuB,GA0BC,KA1BD;AAAA;;AAAA,O;;AA6BrBpD,MAAAA,2B,GAA8B,IAAIkC,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,C;AAgD9B7I,MAAAA,e,GAAN,MAAMA,eAAN,CAAsB;AAAA;AAClB;AADkB,eAEDgK,MAFC,GAEgC,EAFhC;AAGlB;AAHkB,eAIDC,uBAJC,GAIqD,EAJrD;AAMlB;AANkB,eAODC,OAPC,GAOSvB,MAAM,CAACwB,MAAP,CAAc,CAAd,EAAiB,CAAjB,EAAoB,CAApB,EAAuB,CAAvB,CAPT;AAAA,eAQDC,YARC,GAQc,IAAI1B,IAAJ,EARd;AAAA,eASD2B,0BATC,GAS4B,IAAI3B,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,GAAnB,EAAwB,GAAxB,EAA6B,GAA7B,EAAkC,GAAlC,CAT5B;AAAA;;AAWlB;AACA;AACA;AACO4B,QAAAA,UAAU,CAAC7F,KAAD,EAA8B8F,OAA9B,EAAyDC,SAAzD,EAAiF;AAC9F;AACA,eAAKR,MAAL,CAAYvC,MAAZ,GAAqB,CAArB;AACA,eAAKwC,uBAAL,CAA6BxC,MAA7B,GAAsC,CAAtC,CAH8F,CAI9F;;AACA,eAAK,MAAM3G,KAAX,IAAoB2D,KAAK,CAACgG,UAA1B,EAAsC;AAClC,gBAAI3J,KAAK,CAAC4J,KAAV,EAAiB;AACb;AACH;;AACD/B,YAAAA,MAAM,CAACvE,GAAP,CAAW,KAAK8F,OAAhB,EAAyBpJ,KAAK,CAAC6J,QAAN,CAAe1F,CAAxC,EAA2CnE,KAAK,CAAC6J,QAAN,CAAeC,CAA1D,EAA6D9J,KAAK,CAAC6J,QAAN,CAAeE,CAA5E,EAA+E/J,KAAK,CAACgK,KAArF;;AACA,gBAAIlC,SAAS,CAACmC,aAAV,CAAwB,KAAKb,OAA7B,EAAsCK,OAAtC,CAAJ,EAAoD;AAChD,kBAAIzJ,KAAK,CAAC6C,aAAV,EAAyB;AACrB,qBAAKsG,uBAAL,CAA6Be,IAA7B,CAAkClK,KAAlC;AACH,eAFD,MAEO;AACH,qBAAKkJ,MAAL,CAAYgB,IAAZ,CAAiBlK,KAAjB;AACH;AACJ;AACJ,WAjB6F,CAkB9F;;;AACA,eAAK,MAAMA,KAAX,IAAoB2D,KAAK,CAACwG,YAA1B,EAAwC;AACpC,gBAAInK,KAAK,CAAC4J,KAAV,EAAiB;AACb;AACH;;AACD/B,YAAAA,MAAM,CAACvE,GAAP,CAAW,KAAK8F,OAAhB,EAAyBpJ,KAAK,CAAC6J,QAAN,CAAe1F,CAAxC,EAA2CnE,KAAK,CAAC6J,QAAN,CAAeC,CAA1D,EAA6D9J,KAAK,CAAC6J,QAAN,CAAeE,CAA5E,EAA+E/J,KAAK,CAACgK,KAArF;;AACA,gBAAIlC,SAAS,CAACmC,aAAV,CAAwB,KAAKb,OAA7B,EAAsCK,OAAtC,CAAJ,EAAoD;AAChD,mBAAKP,MAAL,CAAYgB,IAAZ,CAAiBlK,KAAjB;AACH;AACJ,WA3B6F,CA4B9F;;;AACA,eAAK,MAAMA,KAAX,IAAoB2D,KAAK,CAACyG,WAA1B,EAAuC;AACnC,gBAAIpK,KAAK,CAAC4J,KAAV,EAAiB;AACb;AACH;;AACD/B,YAAAA,MAAM,CAACvE,GAAP,CAAW,KAAK8F,OAAhB,EAAyBpJ,KAAK,CAAC6J,QAAN,CAAe1F,CAAxC,EAA2CnE,KAAK,CAAC6J,QAAN,CAAeC,CAA1D,EAA6D9J,KAAK,CAAC6J,QAAN,CAAeE,CAA5E,EAA+E/J,KAAK,CAACgK,KAArF;;AACA,gBAAIlC,SAAS,CAACmC,aAAV,CAAwB,KAAKb,OAA7B,EAAsCK,OAAtC,CAAJ,EAAoD;AAChD,mBAAKP,MAAL,CAAYgB,IAAZ,CAAiBlK,KAAjB;AACH;AACJ,WArC6F,CAsC9F;;;AACA,eAAK,MAAMA,KAAX,IAAoB2D,KAAK,CAAC0G,eAA1B,EAA2C;AACvCzC,YAAAA,IAAI,CAAC0C,SAAL,CAAe,KAAKhB,YAApB,EAAkC,KAAKC,0BAAvC,EAAmEvJ,KAAK,CAACuK,IAAN,CAAYC,cAAZ,EAAnE;;AACA,gBAAI1C,SAAS,CAAC2C,WAAV,CAAsB,KAAKnB,YAA3B,EAAyCG,OAAzC,CAAJ,EAAuD;AACnD,mBAAKP,MAAL,CAAYgB,IAAZ,CAAiBlK,KAAjB;AACH;AACJ;;AAED,cAAI0J,SAAJ,EAAe;AACX,iBAAKP,uBAAL,CAA6B5E,IAA7B,CACI,CAACmG,GAAD,EAAMC,GAAN,KAActD,IAAI,CAACuD,eAAL,CAAqBlB,SAArB,EAAgCgB,GAAG,CAACb,QAApC,IAAgDxC,IAAI,CAACuD,eAAL,CAAqBlB,SAArB,EAAgCiB,GAAG,CAACd,QAApC,CADlE;AAGH;AACJ;;AACOgB,QAAAA,eAAe,CAACnL,MAAD,EAAgCyF,IAAhC,EAA8E;AACjG,eAAK,MAAMnF,KAAX,IAAoB,KAAKkJ,MAAzB,EAAiC;AAC7B,kBAAM4B,KAAK,GAAG3F,IAAI,CAACY,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoB8E,KAAlC,EAAyC,aAAzC,CAAd;;AACA,oBAAQ/K,KAAK,CAACyD,IAAd;AACI,mBAAK0E,SAAS,CAAC6C,MAAf;AACIF,gBAAAA,KAAK,CAACG,IAAN,GAAa,cAAb;AACA;;AACJ,mBAAK9C,SAAS,CAAC+C,IAAf;AACIJ,gBAAAA,KAAK,CAACG,IAAN,GAAa,YAAb;AACA;;AACJ,mBAAK9C,SAAS,CAACgD,KAAf;AACIL,gBAAAA,KAAK,CAACG,IAAN,GAAa,aAAb;AACA;;AACJ,mBAAK9C,SAAS,CAACiD,kBAAf;AACIN,gBAAAA,KAAK,CAACG,IAAN,GAAa,0BAAb;AACA;;AACJ;AACIH,gBAAAA,KAAK,CAACG,IAAN,GAAa,eAAb;AAdR;;AAgBAH,YAAAA,KAAK,CAACO,QAAN,CACI3L,MADJ,EAEIsG,SAAS,CAACsF,UAAV,CAAqBP,KAFzB,EAGI/K,KAHJ;AAKH;AACJ;;AACMuL,QAAAA,wBAAwB,CAC3BpK,GAD2B,EAE3BzB,MAF2B,EAG3B8L,gBAH2B,EAIvB;AACJ,cAAIC,CAAC,GAAG,CAAR;;AACA,eAAK,MAAMzL,KAAX,IAAoB,KAAKmJ,uBAAzB,EAAkD;AAC9C,kBAAM9F,aAAa,GAAGlC,GAAG,CAACmB,iBAAJ,CAAsBM,OAAtB,CAA8BW,IAApD;AACA,kBAAMmI,UAAU,GAAGvK,GAAG,CAACiE,aAAJ,CAAkB/B,aAAa,CAACc,CAAhC,EAAmCd,aAAa,CAACyG,CAAjD,EAAoD,SAApD,CAAnB;AACA4B,YAAAA,UAAU,CAACT,IAAX,GAAmB,sBAAqBQ,CAAE,EAA1C;AACAC,YAAAA,UAAU,CAACnG,eAAX,CAA4B,gBAAekG,CAAE,EAA7C,EAAgDhG,MAAM,CAACC,KAAvD,EAA8DC,OAAO,CAACC,KAAtE,EAA6E,IAAImC,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAA7E;AACA2D,YAAAA,UAAU,CAACC,eAAX,CAA4B,kBAAiBF,CAAE,EAA/C,EAAkDhG,MAAM,CAACC,KAAzD,EAAgEC,OAAO,CAACiG,OAAxE;AACAF,YAAAA,UAAU,CAAC3F,QAAX,CAAoBC,SAAS,CAACC,SAAV,CAAoB4F,IAAxC,EAA8C,eAA9C,EACKR,QADL,CACc3L,MADd,EACsBsG,SAAS,CAACsF,UAAV,CAAqBpF,MAArB,GAA8BF,SAAS,CAACsF,UAAV,CAAqBQ,IAAnD,GAA0D9F,SAAS,CAACsF,UAAV,CAAqBS,aADrG,EAEKC,eAFL,CAEqBhM,KAFrB;AAGA,cAAEyL,CAAF;;AACA,gBAAIA,CAAC,IAAID,gBAAT,EAA2B;AACvB;AACH;AACJ;AACJ;;AACMS,QAAAA,cAAc,CAAC9G,IAAD,EACjBzF,MADiB,EACc8L,gBADd,EAC8C;AAC/D,eAAKX,eAAL,CAAqBnL,MAArB,EAA6ByF,IAA7B;;AACA,cAAIsG,CAAC,GAAG,CAAR;;AACA,eAAK,MAAMzL,KAAX,IAAoB,KAAKmJ,uBAAzB,EAAkD;AAC9C;AACA;AACA;AACAhE,YAAAA,IAAI,CAACW,UAAL,CAAiB,gBAAe2F,CAAE,EAAlC,EAAqC,kBAArC;AACA,kBAAMX,KAAK,GAAG3F,IAAI,CAACY,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoB8E,KAAlC,EAAyC,aAAzC,CAAd;AACAD,YAAAA,KAAK,CAACO,QAAN,CAAe3L,MAAf,EAAuBsG,SAAS,CAACsF,UAAV,CAAqBP,KAA5C,EAAmD/K,KAAnD;AACA,cAAEyL,CAAF;;AACA,gBAAIA,CAAC,IAAID,gBAAT,EAA2B;AACvB;AACH;AACJ;AACJ,SAjIiB,CAmIlB;AACA;AACA;;;AACOU,QAAAA,cAAc,CACjB1G,SADiB,EAEjBiD,gBAFiB,EAGjB0D,mBAHiB,EAIjB5F,EAJiB,EAIL;AACZ3F,QAAAA,KALiB,EAMjBG,MANiB,EAOjBrB,MAPiB,EAQjB0M,QARiB,EASjBjL,GATiB,EAUjBgE,IAViB,EAWe;AAChC,eAAK0F,eAAL,CAAqBnL,MAArB,EAA6ByF,IAA7B;;AAEA,cAAIkH,KAAK,GAAG,CAAZ;AACA,gBAAMhJ,aAAa,GAAGlC,GAAG,CAACmB,iBAAJ,CAAsBM,OAAtB,CAA8BW,IAApD;;AACA,eAAK,MAAMvD,KAAX,IAAoB,KAAKmJ,uBAAzB,EAAkD;AAC9C,kBAAMuC,UAAU,GAAGvK,GAAG,CAACiE,aAAJ,CAAkB/B,aAAa,CAACc,CAAhC,EAAmCd,aAAa,CAACyG,CAAjD,EAAoD,SAApD,CAAnB;AACA4B,YAAAA,UAAU,CAACT,IAAX,GAAkB,qBAAlB,CAF8C,CAG9C;;AACAS,YAAAA,UAAU,CAACnG,eAAX,CAA4B,YAAWgB,EAAG,EAA1C,EAA6Cd,MAAM,CAACC,KAApD,EAA2DC,OAAO,CAACC,KAAnE,EAA0E,IAAImC,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAA1E;AACA2D,YAAAA,UAAU,CAACC,eAAX,CAA4B,cAAapF,EAAG,EAA5C,EAA+Cd,MAAM,CAACC,KAAtD,EAA6DC,OAAO,CAACiG,OAArE;AACAF,YAAAA,UAAU,CAAC3F,QAAX,CAAoBC,SAAS,CAACC,SAAV,CAAoB4F,IAAxC,EAA8C,eAA9C,EACKR,QADL,CACc3L,MADd,EACsBsG,SAAS,CAACsF,UAAV,CAAqBpF,MAArB,GAA8BF,SAAS,CAACsF,UAAV,CAAqBQ,IAAnD,GAA0D9F,SAAS,CAACsF,UAAV,CAAqBS,aADrG,EAEKC,eAFL,CAEqBhM,KAFrB,EAN8C,CAU9C;AACA;;AACA,cAAEqM,KAAF;AACA,kBAAMC,OAAO,GAAGD,KAAK,KAAK,KAAKlD,uBAAL,CAA6BxC,MAAvC,GACVwF,mBADU,GAEVxG,OAAO,CAACC,KAFd;AAIAT,YAAAA,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAP;AACAoE,YAAAA,IAAI,CAAC8F,IAAL,GAAY,wBAAZ;AACA9F,YAAAA,IAAI,CAACoH,WAAL,CAAiBH,QAAjB;AACAjH,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAAC+G,IAAvC;AACArH,YAAAA,IAAI,CAACwG,eAAL,CAAqBlD,gBAArB,EAAuChD,MAAM,CAAC+G,IAA9C,EAAoDF,OAApD;AACAnH,YAAAA,IAAI,CAACW,UAAL,CAAiB,YAAWS,EAAG,EAA/B,EAAkC,kBAAlC;AACA,kBAAMuE,KAAK,GAAG3F,IAAI,CAACY,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoB8E,KAAlC,EAAyC,aAAzC,CAAd;AACAD,YAAAA,KAAK,CAACO,QAAN,CACI3L,MADJ,EAEIsG,SAAS,CAACsF,UAAV,CAAqBP,KAFzB,EAGI/K,KAHJ;AAKH;;AACD,iBAAOmF,IAAP;AACH;;AAEMsH,QAAAA,2BAA2B,GAAY;AAC1C,iBAAO,KAAKtD,uBAAL,CAA6BxC,MAA7B,GAAsC,CAA7C;AACH;;AAzLiB,O;;2CAoMTxH,yB,GAAN,MAAMA,yBAAN,CAAyE;AAAA;AAAA,eA6jB3DuN,eA7jB2D,GA6jBzC,IAAIxN,eAAJ,EA7jByC;AAAA,eA8jB3DyN,SA9jB2D,GA8jB/C,IAAI1E,QAAJ,EA9jB+C;AAAA,eA+jB3D2E,WA/jB2D,GA+jB7C,IAAI7E,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CA/jB6C;AAAA,eAgkB3D8E,0BAhkB2D,GAgkB9B,IAAIxF,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAhkB8B;AAAA;;AAG5E3C,QAAAA,cAAc,GAAW;AACrB,iBAAOvF,yBAAyB,CAAC2N,WAAjC;AACH;;AACDlI,QAAAA,cAAc,GAAW;AACrB,iBAAOzF,yBAAyB,CAAC4N,WAAjC;AACH;;AACDC,QAAAA,YAAY,CACRtN,MADQ,EAERuN,eAFQ,EAGRlI,aAHQ,EAGiD;AACzD;AACAA,UAAAA,aAAa,CAACmI,wBAAd,GAAyCD,eAAe,CAACpK,aAAhB,IAClC,CAACoK,eAAe,CAACzJ,eADiB,IAElC,CAAC,CAAC9D,MAAM,CAACiE,KAFyB,IAGlC,CAAC,CAACjE,MAAM,CAACiE,KAAP,CAAawJ,SAHmB,IAIlCzN,MAAM,CAACiE,KAAP,CAAawJ,SAAb,CAAuBtK,aAJ9B;AAMAkC,UAAAA,aAAa,CAACqI,8BAAd,GAA+CH,eAAe,CAACpK,aAAhB,IACxCoK,eAAe,CAACzJ,eADwB,IAExC,CAAC,CAAC9D,MAAM,CAACiE,KAF+B,IAGxC,CAAC,CAACjE,MAAM,CAACiE,KAAP,CAAawJ,SAHyB,IAIxCzN,MAAM,CAACiE,KAAP,CAAawJ,SAAb,CAAuBtK,aAJ9B,CARyD,CAczD;;AACAkC,UAAAA,aAAa,CAACsI,2BAAd,GAA4CtI,aAAa,CAACwD,gBAAd,IACrC7I,MAAM,CAAC4N,WAAP,KAAuBpF,WAAW,CAACqF,UADE,IAErC7N,MAAM,CAAC4N,WAAP,KAAuBpF,WAAW,CAACsF,SAF1C,CAfyD,CAmBzD;;AACAzI,UAAAA,aAAa,CAAC0I,UAAd,GAA2B1I,aAAa,CAACuD,QAAd,CAAuBoF,IAAvB,CAA4B5K,OAA5B,IACpB,CAACiC,aAAa,CAACkE,qBADK,CACiB;AADjB,aAEpB,CAACgE,eAAe,CAACvL,KAFG,CAEG;AAFH,aAGpB,CAACuL,eAAe,CAACpL,QAHxB,CApByD,CAyBzD;;AACAkD,UAAAA,aAAa,CAAC4I,uBAAd,GACMV,eAAe,CAAC7K,QAAhB,IAA4B2C,aAAa,CAAC0I,UADhD;AAGA,YAAE1I,aAAa,CAAC6D,eAAhB;AACH;;AACDgF,QAAAA,YAAY,CACRzM,GADQ,EAER2D,UAFQ,EAGRC,aAHQ,EAIR8I,MAJQ,EAKRnO,MALQ,EAMR2F,WANQ,EAORC,YAPQ,EAOoB;AAC5B,gBAAMwI,aAAa,GAAG9H,SAAS,CAAC8H,aAAhC;AACA,gBAAMC,iBAAiB,GAAG/H,SAAS,CAAC+H,iBAApC;AACA,gBAAMxH,EAAE,GAAGsH,MAAM,CAACrF,cAAlB;AACA,gBAAMF,QAAQ,GAAGvD,aAAa,CAACuD,QAA/B;AAEA,gBAAM1H,KAAK,GAAGmE,aAAa,CAAC8D,kBAAd,GACRhI,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWqE,WAAW,GAAGN,aAAa,CAAC+D,YAAvC,CAAT,EAA+D,CAA/D,CADQ,GAERzD,WAFN;AAGA,gBAAMtE,MAAM,GAAGgE,aAAa,CAAC8D,kBAAd,GACThI,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWsE,YAAY,GAAGP,aAAa,CAAC+D,YAAxC,CAAT,EAAgE,CAAhE,CADS,GAETxD,YAFN,CAT4B,CAa5B;;AACA,cAAIP,aAAa,CAAC0I,UAAlB,EAA8B;AAC1B;AACA;AACA;AACA,gBAAI1I,aAAa,CAACgE,SAAlB,EAA6B;AACzB5H,cAAAA,GAAG,CAAC2E,UAAJ,CAAgB,eAAcS,EAAG,EAAjC,EAAoCyB,WAAW,CAACgG,KAAhD,EAAuDjJ,aAAa,CAACiE,cAArE,EAAqFpI,KAArF,EAA4FG,MAA5F,EAAoG,CAApG,EAAuG,CAAvG,EAA0G,CAA1G,EACIuH,QAAQ,CAACoF,IAAT,CAAcO,WADlB,EAC+BH,aAAa,CAACI,gBAD7C,EAC+DH,iBAAiB,CAACI,UADjF;AAEH,aAHD,MAGO;AACHhN,cAAAA,GAAG,CAAC2E,UAAJ,CAAgB,eAAcS,EAAG,EAAjC,EAAoCyB,WAAW,CAACgG,KAAhD,EAAuD9K,MAAM,CAACE,KAA9D,EAAqExC,KAArE,EAA4EG,MAA5E,EAAoF,CAApF,EAAuF,CAAvF,EAA0F,CAA1F,EACIuH,QAAQ,CAACoF,IAAT,CAAcO,WADlB,EAC+BH,aAAa,CAACI,gBAD7C,EAC+DH,iBAAiB,CAACI,UADjF;AAEH;;AACDhN,YAAAA,GAAG,CAAC2E,UAAJ,CAAgB,mBAAkBS,EAAG,EAArC,EAAwCyB,WAAW,CAACgG,KAApD,EAA2D9K,MAAM,CAACe,aAAlE,EAAiFrD,KAAjF,EAAwFG,MAAxF,EAAgG,CAAhG,EAAmG,CAAnG,EAAsG,CAAtG,EACIuH,QAAQ,CAACoF,IAAT,CAAcO,WADlB,EAC+BH,aAAa,CAACM,wBAD7C,EACuEL,iBAAiB,CAACI,UADzF;AAEH,WA3B2B,CA6B5B;;;AACAhN,UAAAA,GAAG,CAACoE,eAAJ,CACK,YAAWgB,EAAG,EADnB,EAEIzB,UAAU,CAAC/B,eAFf,EAGI+B,UAAU,CAACzB,aAAX,CAAyBc,CAH7B,EAIIW,UAAU,CAACzB,aAAX,CAAyByG,CAJ7B;AAMA3I,UAAAA,GAAG,CAACwK,eAAJ,CACK,cAAapF,EAAG,EADrB,EAEIrD,MAAM,CAACe,aAFX,EAGIa,UAAU,CAACzB,aAAX,CAAyBc,CAH7B,EAIIW,UAAU,CAACzB,aAAX,CAAyByG,CAJ7B,EApC4B,CA2C5B;;AACA,cAAI/E,aAAa,CAAC4I,uBAAlB,EAA2C;AACvC,kBAAMtB,KAAK,GAAGvH,UAAU,CAACsD,4BAAzB;;AACA,iBAAK,IAAIqD,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAKY,KAAtB,EAA6B,EAAEZ,CAA/B,EAAkC;AAC9BtK,cAAAA,GAAG,CAACoE,eAAJ,CACK,gBAAekG,CAAE,EADtB,EAEI3G,UAAU,CAAC/B,eAFf,EAGI+B,UAAU,CAACzB,aAAX,CAAyBc,CAH7B,EAIIW,UAAU,CAACzB,aAAX,CAAyByG,CAJ7B;AAMA3I,cAAAA,GAAG,CAACwK,eAAJ,CACK,kBAAiBF,CAAE,EADxB,EAEIvI,MAAM,CAACe,aAFX,EAGIa,UAAU,CAACzB,aAAX,CAAyBc,CAH7B,EAIIW,UAAU,CAACzB,aAAX,CAAyByG,CAJ7B;AAMH;AACJ;AACJ;;AACDuE,QAAAA,KAAK,CACDlN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD4O,OALC,EAKuE;AACxE;AACAnN,UAAAA,GAAG,CAACoN,OAAJ,CAAY,YAAZ,EAA0BzJ,UAAU,CAACZ,QAArC;AAEA,gBAAMqC,EAAE,GAAG7G,MAAM,CAACmO,MAAP,CAAcrF,cAAzB;AAEA,gBAAM7E,KAAK,GAAGjE,MAAM,CAACiE,KAArB;AACA,gBAAMwJ,SAAS,GAAGxJ,KAAK,CAACwJ,SAAxB;AAEA,YAAEpI,aAAa,CAAC6D,eAAhB;AACA3D,UAAAA,MAAM,CAACF,aAAa,CAAC6D,eAAd,IAAiC,CAAlC,CAAN,CAVwE,CAYxE;;AACA,eAAK8D,eAAL,CAAqBlD,UAArB,CAAgC7F,KAAhC,EAAuCjE,MAAM,CAAC+J,OAA9C,EAbwE,CAexE;;AACA,cAAI1E,aAAa,CAACmI,wBAAlB,EAA4C;AACxCjI,YAAAA,MAAM,CAAC,CAAC,CAACkI,SAAH,CAAN;;AACA,iBAAKqB,yBAAL,CAA+BrN,GAA/B,EAAoC2D,UAApC,EAAgDyB,EAAhD,EAAoD4G,SAApD,EAA+DzN,MAA/D;AACH,WAnBuE,CAqBxE;;;AACA,cAAIqF,aAAa,CAAC4I,uBAAlB,EAA2C;AACvC;AACA;AACA,iBAAKjB,eAAL,CAAqBnB,wBAArB,CACIpK,GADJ,EACSzB,MADT,EACiBoF,UAAU,CAACsD,4BAD5B;AAEH;;AAED,eAAKqG,4BAAL,CAAkCtN,GAAlC,EAAuC4D,aAAvC,EAAsDwB,EAAtD,EAA0D4G,SAA1D,EAAqEzN,MAAM,CAACiE,KAA5E;;AAEA,cAAIoB,aAAa,CAAC6D,eAAd,GAAgC,CAAhC,IAAqC7D,aAAa,CAAC8D,kBAAvD,EAA2E;AACvEyF,YAAAA,OAAO,CAAC9I,SAAR,GAAoBT,aAAa,CAAC8D,kBAAd,GACb,mBAAkBtC,EAAG,EADR,GAEb,aAAYA,EAAG,EAFtB;AAGA+H,YAAAA,OAAO,CAAC7F,gBAAR,GAA2B1D,aAAa,CAAC8D,kBAAd,GACpB,oBAAmBtC,EAAG,EADF,GAEpB,cAAaA,EAAG,EAFvB;AAGH,WAPD,MAOO;AACH+H,YAAAA,OAAO,CAAC9I,SAAR,GAAoBT,aAAa,CAACS,SAAlC;AACA8I,YAAAA,OAAO,CAAC7F,gBAAR,GAA2B1D,aAAa,CAAC0D,gBAAzC;AACH;;AAED,gBAAMtD,IAAI,GAAG,KAAKuJ,yBAAL,CACTvN,GADS,EACJ2D,UADI,EACQC,aADR,EACuBwB,EADvB,EAC2B7G,MAD3B,EAETqF,aAAa,CAACnE,KAFL,EAEYmE,aAAa,CAAChE,MAF1B,EAEkCoM,SAFlC,EAGTmB,OAAO,CAAC9I,SAHC,EAGU8I,OAAO,CAAC7F,gBAHlB,EAIT,CAAC1D,aAAa,CAAC0I,UAJN,EAKT1I,aAAa,CAACkE,qBAAd,GAAsCtD,OAAO,CAACC,KAA9C,GAAsDD,OAAO,CAACiG,OALrD,CAAb;;AAOA,cAAI,CAAC7G,aAAa,CAACkE,qBAAnB,EAA0C;AACtCqF,YAAAA,OAAO,CAAC7F,gBAAR,GAA2B,EAA3B;AACH;;AAED,cAAI1D,aAAa,CAAC6D,eAAd,KAAkC,CAAlC,IAAuC7D,aAAa,CAAC8D,kBAAzD,EAA6E;AACzE,mBAAOhE,mBAAmB,CAAC1D,GAAD,EAAM2D,UAAN,EAAkBC,aAAlB,EAAiCuJ,OAAO,CAAC9I,SAAzC,CAA1B;AACH,WAFD,MAEO;AACH,mBAAOL,IAAP;AACH;AACJ;;AACOqJ,QAAAA,yBAAyB,CAC7BrN,GAD6B,EAE7B2D,UAF6B,EAG7ByB,EAH6B,EAI7BvG,KAJ6B,EAK7BN,MAL6B,EAMzB;AACJ,gBAAMuG,SAAS,GAAGD,SAAS,CAACC,SAA5B;AACA,gBAAMqF,UAAU,GAAGtF,SAAS,CAACsF,UAA7B,CAFI,CAGJ;AACA;AACA;;AACA,gBAAMqD,UAAU,GAAGxN,GAAG,CAACmB,iBAAJ,CAAsBM,OAAtB,CAA8BW,IAAjD;AACA,gBAAM3C,KAAK,GAAG+N,UAAU,CAACxK,CAAzB;AACA,gBAAMpD,MAAM,GAAG4N,UAAU,CAAC7E,CAA1B;AAEA,gBAAMsC,QAAQ,GAAG,KAAKO,SAAtB;AACAP,UAAAA,QAAQ,CAAC1L,IAAT,GAAgB0L,QAAQ,CAACzL,GAAT,GAAe,CAA/B;AACAyL,UAAAA,QAAQ,CAACxL,KAAT,GAAiBA,KAAjB;AACAwL,UAAAA,QAAQ,CAACrL,MAAT,GAAkBA,MAAlB,CAbI,CAeJ;AACA;AACA;;AACA,gBAAMoE,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAb;AACAoE,UAAAA,IAAI,CAAC8F,IAAL,GAAY,mBAAZ;AACA9F,UAAAA,IAAI,CAACI,eAAL,CAAsB,YAAWgB,EAAG,EAApC,EAAuCd,MAAM,CAACC,KAA9C,EAAqDC,OAAO,CAACC,KAA7D,EAAoE,IAAImC,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAApE;AACA5C,UAAAA,IAAI,CAACwG,eAAL,CAAsB,cAAapF,EAAG,EAAtC,EAAyCd,MAAM,CAACC,KAAhD,EAAuDC,OAAO,CAACiG,OAA/D;AACA,gBAAMrL,QAAQ,GAAGY,GAAG,CAACmB,iBAAJ,CAAsBsM,YAAtB,GAAqC5O,KAAK,CAACO,QAA3C,GAAsD,CAAvE,CAtBI,CAwBJ;;AACA,eAAK,IAAIJ,KAAK,GAAG,CAAjB,EAAoBA,KAAK,KAAKI,QAA9B,EAAwC,EAAEJ,KAA1C,EAAiD;AAC7CJ,YAAAA,uBAAuB,CAACC,KAAD,EAAQY,KAAR,EAAeG,MAAf,EAAuBZ,KAAvB,EAA8B,KAAKwM,SAAnC,EAA8C7H,UAAU,CAACzE,gBAAzD,CAAvB;AACA,kBAAMyK,KAAK,GAAG3F,IAAI,CAACY,QAAL,CAAcE,SAAS,CAAC4F,IAAxB,EAA8B,eAA9B,CAAd;;AACA,gBAAI,CAAC/G,UAAU,CAAC5C,QAAhB,EAA0B;AAAE;AACxB4I,cAAAA,KAAK,CAACyB,WAAN,CAAkB,KAAKI,SAAvB;AACH;;AACD7B,YAAAA,KAAK,CACAO,QADL,CACc3L,MADd,EACsB4L,UAAU,CAACpF,MAAX,GAAoBoF,UAAU,CAACQ,IAA/B,GAAsCR,UAAU,CAACS,aADvE,EAEKC,eAFL,CAEqBhM,KAFrB,EAE4BG,KAF5B;AAGH;AACJ;;AACOsO,QAAAA,4BAA4B,CAChCtN,GADgC,EAEhC4D,aAFgC,EAGhCwB,EAHgC,EAIhC4G,SAJgC,EAKhCxJ,KALgC,EAM5B;AACJ,gBAAMkL,sBAAsB,GAAG/H,QAAQ,CAACgI,QAAT,CAAkBD,sBAAjD;;AACA,cAAI,CAACA,sBAAL,EAA6B;AACzB;AACH;;AACD,gBAAMd,iBAAiB,GAAG/H,SAAS,CAAC+H,iBAApC;AACA,gBAAMgB,MAAM,GAAGF,sBAAsB,CAACG,SAAvB,EAAf;AACA,gBAAMC,aAAa,GAAG,CAAtB;AACA,cAAIC,OAAO,GAAG,CAAd;;AACA,eAAK,MAAMC,KAAX,IAAoBJ,MAApB,EAA4B;AACxB,gBAAI,CAACI,KAAK,CAACC,UAAX,EAAuB;AACnB;AACH;;AACD,kBAAMC,IAAI,GAAGF,KAAK,CAACG,UAAN,EAAb;AACA,kBAAM1O,KAAK,GAAGC,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWqO,IAAI,CAAClL,CAAhB,CAAT,EAA6B,CAA7B,CAAd;AACA,kBAAMpD,MAAM,GAAGF,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWqO,IAAI,CAACvF,CAAhB,CAAT,EAA6B,CAA7B,CAAf;;AAEA,gBAAIqF,KAAK,CAACI,SAAN,KAAoB7L,QAAQ,CAACC,KAAT,CAAe6L,SAAf,CAAyBC,MAAjD,EAAyD;AACrD,kBAAI,CAAC1K,aAAa,CAACsI,2BAAnB,EAAgD;AAC5C;AACH;;AACD,oBAAMQ,MAA6B,GAAGsB,KAAK,CAACO,qBAAN,CAA6B7B,MAAnE;AACA,oBAAMrI,SAAS,GAAI,gBAAe0J,OAAQ,EAA1C;AACA,oBAAMzG,gBAAgB,GAAI,gBAAeyG,OAAQ,EAAjD,CANqD,CAOrD;;AACA/N,cAAAA,GAAG,CAACwO,eAAJ,CAAoBnK,SAApB,EACIT,aAAa,CAACiE,cADlB,EACkCpI,KADlC,EACyCG,MADzC,EACiD8M,MADjD;AAEA1M,cAAAA,GAAG,CAACwK,eAAJ,CAAoBlD,gBAApB,EACI1G,GAAG,CAACmB,MAAJ,CAAWe,aADf,EAC8BrD,KAD9B,EACqCG,MADrC,EAC6CgN,iBAAiB,CAACI,UAD/D,EAVqD,CAarD;;AACA,oBAAMyB,SAAS,GAAGzO,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAlB;AACA6O,cAAAA,SAAS,CAAC3E,IAAV,GAAkB,wBAAuBiE,OAAQ,EAAjD;;AACA,mBAAKW,yBAAL,CAA+BD,SAA/B,EAA0C7K,aAA1C,EAAyDwB,EAAzD,EAA6D4I,KAAK,CAACzP,MAAnE,EACI8F,SADJ,EACeiD,gBADf,EACiC0E,SADjC,EAC4CxJ,KAD5C;AAEH,aAlBD,MAkBO,IAAI8D,MAAJ,EAAY;AACf,mBAAK,IAAIqI,OAAO,GAAG,CAAnB,EAAsBA,OAAO,GAAGX,KAAK,CAACY,iBAAN,CAAwBpJ,MAAxD,EAAgEmJ,OAAO,EAAvE,EAA2E;AACvEX,gBAAAA,KAAK,CAACa,eAAN,CAAsBF,OAAtB;AACA,sBAAMjC,MAA6B,GAAGsB,KAAK,CAACY,iBAAN,CAAwBD,OAAxB,EAAiCjC,MAAvE;AACA,sBAAMrI,SAAS,GAAI,cAAa0J,OAAQ,GAAEY,OAAQ,EAAlD;AACA,sBAAMrH,gBAAgB,GAAI,cAAayG,OAAQ,GAAEY,OAAQ,EAAzD,CAJuE,CAKvE;;AACA3O,gBAAAA,GAAG,CAACwO,eAAJ,CAAoBnK,SAApB,EACIT,aAAa,CAACiE,cADlB,EACkCpI,KADlC,EACyCG,MADzC,EACiD8M,MADjD;AAEA1M,gBAAAA,GAAG,CAACwK,eAAJ,CAAoBlD,gBAApB,EACI1G,GAAG,CAACmB,MAAJ,CAAWe,aADf,EAC8BrD,KAD9B,EACqCG,MADrC,EAC6CgN,iBAAiB,CAACI,UAD/D,EARuE,CAWvE;;AACA,sBAAMyB,SAAS,GAAGzO,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAlB;AACA6O,gBAAAA,SAAS,CAAC3E,IAAV,GAAkB,YAAWiE,OAAQ,GAAEY,OAAQ,EAA/C;;AACA,qBAAKD,yBAAL,CAA+BD,SAA/B,EAA0C7K,aAA1C,EAAyDwB,EAAzD,EAA6D4I,KAAK,CAACzP,MAAnE,EACI8F,SADJ,EACeiD,gBADf,EACiC0E,SADjC,EAC4CxJ,KAD5C;AAEH;;AACDwL,cAAAA,KAAK,CAACC,UAAN,GAAmB,KAAnB;AACH;;AACD,cAAEF,OAAF;;AACA,gBAAIA,OAAO,KAAKD,aAAhB,EAA+B;AAC3B;AACH;AACJ;AACJ;;AACOY,QAAAA,yBAAyB,CAC7B1K,IAD6B,EAE7BJ,aAF6B,EAG7BwB,EAH6B,EAI7B7G,MAJ6B,EAK7B8F,SAL6B,EAM7BiD,gBAN6B,EAO7B0E,SAP6B,EAQ7BxJ,KAAkC,GAAG,IARR,EASzB;AACJ,gBAAMsC,SAAS,GAAGD,SAAS,CAACC,SAA5B;AACA,gBAAMqF,UAAU,GAAGtF,SAAS,CAACsF,UAA7B,CAFI,CAGJ;;AACA,gBAAM2E,YAAY,GAAGlL,aAAa,CAAC0I,UAAd,GAA2B9H,OAAO,CAACiG,OAAnC,GAA6CjG,OAAO,CAACC,KAA1E,CAJI,CAMJ;;AACA,cAAInG,qBAAqB,CAACC,MAAD,CAAzB,EAAmC;AAC/B,iBAAKmN,0BAAL,CAAgC1I,CAAhC,GAAoCzE,MAAM,CAACwQ,UAAP,CAAkB/L,CAAtD;AACA,iBAAK0I,0BAAL,CAAgC/C,CAAhC,GAAoCpK,MAAM,CAACwQ,UAAP,CAAkBpG,CAAtD;AACA,iBAAK+C,0BAAL,CAAgC9C,CAAhC,GAAoCrK,MAAM,CAACwQ,UAAP,CAAkBnG,CAAtD;AACA,kBAAMmG,UAAU,GAAGlK,SAAS,CAACmK,QAAV,CAAmB,KAAKtD,0BAAxB,CAAnB;AACA,iBAAKD,WAAL,CAAiBzI,CAAjB,GAAqB+L,UAAU,CAAC/L,CAAhC;AACA,iBAAKyI,WAAL,CAAiB9C,CAAjB,GAAqBoG,UAAU,CAACpG,CAAhC;AACA,iBAAK8C,WAAL,CAAiB7C,CAAjB,GAAqBmG,UAAU,CAACnG,CAAhC;AACA,iBAAK6C,WAAL,CAAiB3M,CAAjB,GAAqBiQ,UAAU,CAACjQ,CAAhC;AACAkF,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CuK,YAA9C,EAA4D,KAAKrD,WAAjE;AACH,WAVD,MAUO;AACHzH,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAAC+G,IAAvC,EAA6CyD,YAA7C;AACH,WAnBG,CAqBJ;;;AACA,cAAIvQ,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aAApC,EAAmD;AAC/CkB,YAAAA,IAAI,CAACwG,eAAL,CACIlD,gBADJ,EAEIhD,MAAM,CAACC,KAFX,EAGIC,OAAO,CAACiG,OAHZ,EAIIlM,MAAM,CAAC0Q,UAJX,EAKI1Q,MAAM,CAAC2Q,YALX,EAMI3Q,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aANpC;AAQH,WATD,MASO;AACHkB,YAAAA,IAAI,CAACwG,eAAL,CAAqBlD,gBAArB,EAAuChD,MAAM,CAAC+G,IAA9C,EAAoD7G,OAAO,CAACiG,OAA5D;AACH,WAjCG,CAmCJ;;;AACA,cAAI7G,aAAa,CAACmI,wBAAlB,EAA4C;AACxC/H,YAAAA,IAAI,CAACW,UAAL,CAAiB,YAAWS,EAAG,EAA/B,EAAkC,cAAlC;AACH,WAtCG,CAwCJ;AAEA;;;AACApB,UAAAA,IAAI,CAACY,QAAL,CAAcE,SAAS,CAAC4F,IAAxB,EAA8B,aAA9B,EAA6C;AAA7C,WACKR,QADL,CACc3L,MADd,EAEQ4L,UAAU,CAACpF,MAAX,GAAoBoF,UAAU,CAACQ,IAA/B,GAAsCR,UAAU,CAACgF,gBAFzD,EAGQnD,SAAS,IAAIoD,SAHrB,EAIQ5M,KAAK,GAAGA,KAAH,GAAW4M,SAJxB;AAKH;;AACO7B,QAAAA,yBAAyB,CAC7BvN,GAD6B,EAE7B2D,UAF6B,EAG7BC,aAH6B,EAI7BwB,EAJ6B,EAK7B7G,MAL6B,EAM7BkB,KAN6B,EAO7BG,MAP6B,EAQ7BoM,SAR6B,EAS7B3H,SAT6B,EAU7BiD,gBAV6B,EAW7B+H,WAAoB,GAAG,KAXM,EAY7BrE,mBAAgC,GAAGxG,OAAO,CAACiG,OAZd,EAaG;AAChC,gBAAM3F,SAAS,GAAGD,SAAS,CAACC,SAA5B;AACA,gBAAMqF,UAAU,GAAGtF,SAAS,CAACsF,UAA7B,CAFgC,CAGhC;AACA;AACA;AACA;;AACA,gBAAM4E,UAAU,GAAGxQ,MAAM,CAACwQ,UAA1B,CAPgC,CAOM;;AACtC,eAAKtD,WAAL,CAAiBzI,CAAjB,GAAqB+L,UAAU,CAAC/L,CAAhC;AACA,eAAKyI,WAAL,CAAiB9C,CAAjB,GAAqBoG,UAAU,CAACpG,CAAhC;AACA,eAAK8C,WAAL,CAAiB7C,CAAjB,GAAqBmG,UAAU,CAACnG,CAAhC;AACA,eAAK6C,WAAL,CAAiB3M,CAAjB,GAAqBiQ,UAAU,CAACjQ,CAAhC,CAXgC,CAahC;;AACA,gBAAMmM,QAAQ,GAAG1M,MAAM,CAAC0M,QAAxB,CAdgC,CAcE;;AAClC,eAAKO,SAAL,CAAejM,IAAf,GAAsBG,IAAI,CAAC4P,KAAL,CAAWrE,QAAQ,CAACjI,CAAT,GAAavD,KAAxB,CAAtB;AACA,eAAK+L,SAAL,CAAehM,GAAf,GAAqBE,IAAI,CAAC4P,KAAL,CAAWrE,QAAQ,CAACtC,CAAT,GAAa/I,MAAxB,CAArB,CAhBgC,CAiBhC;AACA;;AACA,eAAK4L,SAAL,CAAe/L,KAAf,GAAuBC,IAAI,CAACI,GAAL,CAASJ,IAAI,CAAC4P,KAAL,CAAWrE,QAAQ,CAACxL,KAAT,GAAiBA,KAA5B,CAAT,EAA6C,CAA7C,CAAvB;AACA,eAAK+L,SAAL,CAAe5L,MAAf,GAAwBF,IAAI,CAACI,GAAL,CAASJ,IAAI,CAAC4P,KAAL,CAAWrE,QAAQ,CAACrL,MAAT,GAAkBA,MAA7B,CAAT,EAA+C,CAA/C,CAAxB,CApBgC,CAsBhC;;AACA,gBAAM0M,UAAU,GAAG,CAAC+C,WAAD,IAAgBzL,aAAa,CAAC0I,UAAjD;AACAxI,UAAAA,MAAM,CAAC,CAACwI,UAAD,IAAe1I,aAAa,CAAC4I,uBAA9B,CAAN,CAxBgC,CA0BhC;AACA;AACA;;AACA,gBAAMxI,IAAI,GAAGJ,aAAa,CAAC4I,uBAAd,GACP,KAAK+C,6BAAL,CAAmCvP,GAAnC,EAAwC2D,UAAxC,EAAoDC,aAApD,EACEwB,EADF,EACM7G,MADN,EACc+N,UADd,EAC0B7M,KAD1B,EACiCG,MADjC,EACyCoM,SADzC,EAEE3H,SAFF,EAEaiD,gBAFb,EAE+B0D,mBAF/B,CADO,GAIP,KAAKwE,iCAAL,CAAuCxP,GAAvC,EAA4C4D,aAA5C,EACEwB,EADF,EACM7G,MADN,EACckB,KADd,EACqBG,MADrB,EAC6BoM,SAD7B,EAEE3H,SAFF,EAEaiD,gBAFb,EAE+B0D,mBAF/B,CAJN,CA7BgC,CAqChC;;AACA,cAAIpH,aAAa,CAACqI,8BAAlB,EAAkD;AAC9C,iBAAKwD,qBAAL,CAA2BlR,MAA3B,EAAmCyN,SAAnC,EAA8ChI,IAA9C;AACH,WAxC+B,CA0ChC;AACA;AACA;AACA;;;AAEA,gBAAM0L,UAAU,GAAGvF,UAAU,CAACP,KAAX,IACdrL,MAAM,CAACoR,gBAAP,GACKxF,UAAU,CAACyF,QADhB,GAEKzF,UAAU,CAACO,IAHF,CAAnB;AAKA1G,UAAAA,IAAI,CACCY,QADL,CACcE,SAAS,CAAC8E,KADxB,EAEKM,QAFL,CAEc3L,MAFd,EAEsBmR,UAFtB,EAEkC1D,SAAS,IAAIoD,SAF/C;AAIA,iBAAOpL,IAAP;AACH;;AACOuL,QAAAA,6BAA6B,CACjCvP,GADiC,EAEjC2D,UAFiC,EAGjCC,aAHiC,EAIjCwB,EAJiC,EAKjC7G,MALiC,EAMjC+N,UANiC,EAOjC7M,KAPiC,EAQjCG,MARiC,EASjCoM,SATiC,EAUjC3H,SAViC,EAWjCiD,gBAXiC,EAYjC0D,mBAZiC,EAaD;AAChClH,UAAAA,MAAM,CAACF,aAAa,CAAC4I,uBAAf,CAAN,CADgC,CAEhC;AACA;AACA;;AACA,cAAIxI,IAAJ;;AACA,cAAIsI,UAAJ,EAAgB;AACZ,kBAAMuD,gBAAgB,GAAI,eAAczK,EAAG,EAA3C;AACA,kBAAM0K,oBAAoB,GAAI,mBAAkB1K,EAAG,EAAnD;AACA,kBAAM0H,WAAW,GAAGlJ,aAAa,CAACuD,QAAd,CAAuBoF,IAAvB,CAA4BO,WAAhD;AAEA,kBAAMiD,MAAM,GAAG/P,GAAG,CAACgQ,wBAAJ,CAA6BvQ,KAA7B,EAAoCG,MAApC,EAA4CkN,WAA5C,EAAyD,CAAzD,EAA4D,SAA5D,CAAf;AACAiD,YAAAA,MAAM,CAACjG,IAAP,GAAc,iBAAd,CANY,CAQZ;;AACA,iBAAKmG,0BAAL,CAAgCF,MAAhC,EAAwCnM,aAAxC,EAAuDwB,EAAvD,EAA2D7G,MAA3D,EACIsR,gBADJ,EACsBC,oBADtB,EAC4CtL,OAAO,CAACiG,OADpD,EAC6DuB,SAD7D;;AAGA+D,YAAAA,MAAM,CAACG,mBAAP,CAA2BL,gBAA3B,EAA6CxL,SAA7C;AAEAL,YAAAA,IAAI,GAAG+L,MAAP;AACH,WAfD,MAeO;AACH/L,YAAAA,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAP;AACAoE,YAAAA,IAAI,CAAC8F,IAAL,GAAY,aAAZ;;AAEA,iBAAKmG,0BAAL,CAAgCjM,IAAhC,EAAsCJ,aAAtC,EAAqDwB,EAArD,EAAyD7G,MAAzD,EACI8F,SADJ,EACeiD,gBADf,EACiC0D,mBADjC,EACsDgB,SADtD;AAEH;;AACDlI,UAAAA,MAAM,CAACE,IAAI,KAAKoL,SAAV,CAAN,CA5BgC,CA8BhC;;AACA,eAAK7D,eAAL,CAAqBT,cAArB,CACI9G,IADJ,EAEIzF,MAFJ,EAGIoF,UAAU,CAACsD,4BAHf;AAMA,iBAAOjD,IAAP;AACH;;AACOwL,QAAAA,iCAAiC,CACrCxP,GADqC,EAErC4D,aAFqC,EAGrCwB,EAHqC,EAIrC7G,MAJqC,EAKrCkB,KALqC,EAMrCG,MANqC,EAOrCoM,SAPqC,EAQrC3H,SARqC,EASrCiD,gBATqC,EAUrC0D,mBAVqC,EAWL;AAChClH,UAAAA,MAAM,CAAC,CAACF,aAAa,CAAC4I,uBAAhB,CAAN,CADgC,CAGhC;;AACA,cAAIxI,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAX;AACAoE,UAAAA,IAAI,CAAC8F,IAAL,GAAY,aAAZ;AAEA,gBAAMqG,YAAY,GAAG,KAAK5E,eAAL,CAAqBD,2BAArB,KACf9G,OAAO,CAACC,KADO,GAEfuG,mBAFN;;AAIA,eAAKiF,0BAAL,CAAgCjM,IAAhC,EAAsCJ,aAAtC,EACIwB,EADJ,EACQ7G,MADR,EACgB8F,SADhB,EAC2BiD,gBAD3B,EAC6C6I,YAD7C,EAC2DnE,SAD3D,EAXgC,CAchC;;;AACAhI,UAAAA,IAAI,GAAG,KAAKuH,eAAL,CACFR,cADE,CACa1G,SADb,EACwBiD,gBADxB,EAC0C0D,mBAD1C,EAEC5F,EAFD,EAEK3F,KAFL,EAEYG,MAFZ,EAEoBrB,MAFpB,EAE4B,KAAKiN,SAFjC,EAE4CxL,GAF5C,EAEiDgE,IAFjD,CAAP;AAIA,iBAAOA,IAAP;AACH;;AACOiM,QAAAA,0BAA0B,CAC9BjM,IAD8B,EAE9BJ,aAF8B,EAG9BwB,EAH8B,EAI9B7G,MAJ8B,EAK9B8F,SAL8B,EAM9BiD,gBAN8B,EAO9B0D,mBAP8B,EAQ9BgB,SAR8B,EAS9BxJ,KAAkC,GAAG,IATP,EAU1B;AACJ,gBAAMsC,SAAS,GAAGD,SAAS,CAACC,SAA5B;AACA,gBAAMqF,UAAU,GAAGtF,SAAS,CAACsF,UAA7B,CAFI,CAGJ;;AACAnG,UAAAA,IAAI,CAACoH,WAAL,CAAiB,KAAKI,SAAtB;AAEA,gBAAMsD,YAAY,GAAGlL,aAAa,CAAC0I,UAAd,GAA2B9H,OAAO,CAACiG,OAAnC,GAA6CjG,OAAO,CAACC,KAA1E,CANI,CAQJ;;AACA,cAAInG,qBAAqB,CAACC,MAAD,CAAzB,EAAmC;AAC/ByF,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CuK,YAA9C,EAA4D,KAAKrD,WAAjE;AACH,WAFD,MAEO;AACHzH,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAAC+G,IAAvC,EAA6CyD,YAA7C;AACH,WAbG,CAeJ;;;AACA,cAAIzI,KAAJ,EAAW;AACP,gBAAIhC,SAAS,KAAKT,aAAa,CAACS,SAA5B,IACAiD,gBAAgB,KAAK1D,aAAa,CAAC0D,gBADvC,EACyD;AACrDlB,cAAAA,IAAI,CAAC,4DAAD,CAAJ;AACH;AACJ;;AAED,cAAI7H,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aAApC,EAAmD;AAC/CkB,YAAAA,IAAI,CAACwG,eAAL,CACIlD,gBADJ,EAEIhD,MAAM,CAACC,KAFX,EAGIyG,mBAHJ,EAIIzM,MAAM,CAAC0Q,UAJX,EAKI1Q,MAAM,CAAC2Q,YALX,EAMI3Q,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aANpC;AAQH,WATD,MASO;AACHkB,YAAAA,IAAI,CAACwG,eAAL,CAAqBlD,gBAArB,EAAuChD,MAAM,CAAC+G,IAA9C,EAAoDL,mBAApD;AACH,WAlCG,CAoCJ;;;AACA,cAAIpH,aAAa,CAACmI,wBAAlB,EAA4C;AACxC/H,YAAAA,IAAI,CAACW,UAAL,CAAiB,YAAWS,EAAG,EAA/B,EAAkC,cAAlC;AACH,WAvCG,CAyCJ;AAEA;;;AACApB,UAAAA,IAAI,CAACY,QAAL,CAAcE,SAAS,CAAC4F,IAAxB,EAA8B;AAA9B,WACKR,QADL,CACc3L,MADd,EAEQ4L,UAAU,CAACpF,MAAX,GAAoBoF,UAAU,CAACQ,IAFvC,EAGQqB,SAAS,IAAIoD,SAHrB,EAIQ5M,KAAK,GAAGA,KAAH,GAAW4M,SAJxB;AAKH;;AACOK,QAAAA,qBAAqB,CACzBlR,MADyB,EAEzByN,SAFyB,EAGzBhI,IAHyB,EAI3B;AACE,gBAAMc,SAAS,GAAGD,SAAS,CAACC,SAA5B;AACA,gBAAMqF,UAAU,GAAGtF,SAAS,CAACsF,UAA7B;AACAnG,UAAAA,IAAI,CAACY,QAAL,CAAcE,SAAS,CAAC8E,KAAxB,EAA+B,eAA/B,EACKM,QADL,CAEQ3L,MAFR,EAGQ4L,UAAU,CAACS,aAAX,GAA2BT,UAAU,CAACiG,aAAtC,GAAsDjG,UAAU,CAACP,KAHzE,EAIQoC,SAAS,IAAIoD,SAJrB;AAMH;;AA5jB2E,O;;AAAnEpR,MAAAA,yB,CACF2N,W,GAAc,G;AADZ3N,MAAAA,yB,CAEF4N,W,GAAc,G;;yCA+kBZ3N,uB,GAAN,MAAMA,uBAAN,CAAuE;AAAA;AAgV1E;AAhV0E,eAiVzDoS,2BAjVyD,GAiV3B,IAAIzJ,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAjV2B;AAAA,eAkVzD0J,YAlVyD,GAkV1C,IAAInK,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAlV0C;AAAA,eAmVzDoK,aAnVyD,GAmVzC,IAAIpK,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAnVyC;AAAA,eAoVzDqK,YApVyD,GAoV3B,EApV2B;AAAA,eAqVzDC,aArVyD,GAqV1B,EArV0B;AAAA,eAsVzDC,cAtVyD,GAsVzB,EAtVyB;AAwV1E;AAxV0E,eAyVzDC,sBAzVyD,GAyVN,EAzVM;AAAA,eA0VzDC,wBA1VyD,GA0VJ,EA1VI;AAAA,eA2VlEC,iBA3VkE,GA2V3B;AAAE/G,YAAAA,IAAI,EAAE,EAAR;AAAYrK,YAAAA,KAAK,EAAE,CAAnB;AAAsBG,YAAAA,MAAM,EAAE;AAA9B,WA3V2B;AAAA,eA4VlEkR,kBA5VkE,GA4V1B;AAAEhH,YAAAA,IAAI,EAAE,EAAR;AAAYrK,YAAAA,KAAK,EAAE,CAAnB;AAAsBG,YAAAA,MAAM,EAAE;AAA9B,WA5V0B;AAAA;;AAC1E2D,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDE,QAAAA,cAAc,GAAW;AACrB,iBAAO,GAAP;AACH;;AACDoI,QAAAA,YAAY,CACRtN,MADQ,EAERuN,eAFQ,EAGRlI,aAHQ,EAG+C;AACvD,gBAAM;AAAEmN,YAAAA;AAAF,cAAYnN,aAAa,CAACuD,QAAhC;AACA,gBAAM6J,gBAAgB,GAClBD,KAAK,CAACzO,IAAN,KAAe;AAAA;AAAA,sCAAU2O,gBAAzB,IAA6C,CAAC,CAACF,KAAK,CAACG,oBAArD,IACAH,KAAK,CAACzO,IAAN,KAAe;AAAA;AAAA,sCAAU6O,YAAzB,IAAyC,CAAC,CAACJ,KAAK,CAACK,oBAFrD;AAIAxN,UAAAA,aAAa,CAACyN,WAAd,GAA4BN,KAAK,CAACpP,OAAN,IAAiBqP,gBAA7C;;AAEA,cAAIpN,aAAa,CAACyN,WAAlB,EAA+B;AAC3B,cAAEzN,aAAa,CAAC6D,eAAhB;AACH;AACJ;;AACDgF,QAAAA,YAAY,CACRzM,GADQ,EAER2D,UAFQ,EAGRC,aAHQ,EAIR8I,MAJQ,EAI6B;AACrC,cAAI,CAAC9I,aAAa,CAACyN,WAAnB,EAAgC;AAC5B;AACH;;AAED,gBAAM;AAAE5R,YAAAA,KAAF;AAASG,YAAAA,MAAT;AAAiBuH,YAAAA,QAAQ,EAAE;AAAE4J,cAAAA;AAAF;AAA3B,cAAyCnN,aAA/C;AACA,gBAAMwB,EAAE,GAAGsH,MAAM,CAACrF,cAAlB;AACA,gBAAMiK,MAAM,GAAG1N,aAAa,CAACiE,cAA7B;;AAEA,cAAIkJ,KAAK,CAACzO,IAAN,KAAe;AAAA;AAAA,sCAAU2O,gBAA7B,EAA+C;AAC3C,gBAAIM,UAAU,GAAG3N,aAAa,CAACnE,KAA/B;AACA,gBAAI+R,WAAW,GAAG5N,aAAa,CAAChE,MAAhC;;AACA,iBAAK,IAAI0K,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAKyG,KAAK,CAACU,UAAN,GAAmB,CAAzC,EAA4C,EAAEnH,CAA9C,EAAiD;AAC7CiH,cAAAA,UAAU,GAAG7R,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW0R,UAAU,GAAG,CAAxB,CAAT,EAAqC,CAArC,CAAb;AACAC,cAAAA,WAAW,GAAG9R,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW2R,WAAW,GAAG,CAAzB,CAAT,EAAsC,CAAtC,CAAd;AACAxR,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,WAAUgB,EAAG,IAAGkF,CAAE,EAAvC,EAA0CgH,MAA1C,EAAkDC,UAAlD,EAA8DC,WAA9D;AACH;AACJ,WARD,MAQO,IAAIT,KAAK,CAACzO,IAAN,KAAe;AAAA;AAAA,sCAAU6O,YAA7B,EAA2C;AAC9C,kBAAMM,UAAU,GAAGV,KAAK,CAACU,UAAzB;;AACA,iBAAK,IAAInH,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAKmH,UAAU,GAAG,CAAnC,EAAsC,EAAEnH,CAAxC,EAA2C;AACvC;AACA,kBAAIA,CAAC,GAAGmH,UAAR,EAAoB;AAChB,sBAAM/L,KAAK,GAAGhG,IAAI,CAACgS,GAAL,CAAS,GAAT,EAAcpH,CAAC,GAAG,CAAlB,CAAd;AACA,qBAAKsG,wBAAL,CAA8BtG,CAA9B,IAAmC,KAAKqH,aAAL,CAC/B3R,GAD+B,EAE9B,kBAAiBoF,EAAG,GAAEkF,CAAE,EAFM,EAG/B7E,QAAQ,CAAChG,KAAD,EAAQiG,KAAR,CAHuB,EAI/BD,QAAQ,CAAC7F,MAAD,EAAS8F,KAAT,CAJuB,EAK/B4L,MAL+B,CAAnC;AAMH,eAVsC,CAWvC;;;AACA,kBAAIhH,CAAC,GAAGmH,UAAU,GAAG,CAArB,EAAwB;AACpB,sBAAM/L,KAAK,GAAGhG,IAAI,CAACgS,GAAL,CAAS,GAAT,EAAcD,UAAU,GAAGnH,CAAb,GAAiB,CAA/B,CAAd;AACA,qBAAKqG,sBAAL,CAA4BrG,CAA5B,IAAiC,KAAKqH,aAAL,CAC7B3R,GAD6B,EAE5B,gBAAeoF,EAAG,GAAEkF,CAAE,EAFM,EAG7B7E,QAAQ,CAAChG,KAAD,EAAQiG,KAAR,CAHqB,EAI7BD,QAAQ,CAAC7F,MAAD,EAAS8F,KAAT,CAJqB,EAK7B4L,MAL6B,CAAjC;AAMH;AACJ;;AACD,iBAAKR,kBAAL,GAA0B,KAAKa,aAAL,CAAmB3R,GAAnB,EAAyB,gBAAeoF,EAAG,EAA3C,EAA8C3F,KAA9C,EAAqDG,MAArD,EAA6D0R,MAA7D,CAA1B;AACA,iBAAKT,iBAAL,GAAyB,KAAKc,aAAL,CAAmB3R,GAAnB,EAAyB,iBAAgBoF,EAAG,EAA5C,EACrBK,QAAQ,CAAChG,KAAD,EAAQ,GAAR,CADa,EACCgG,QAAQ,CAAC7F,MAAD,EAAS,GAAT,CADT,EACwB0R,MADxB,CAAzB;AAEH;AACJ;;AACOK,QAAAA,aAAa,CACjB3R,GADiB,EAEjB8J,IAFiB,EAEHrK,KAFG,EAEYG,MAFZ,EAE4B0R,MAF5B,EAE+D;AAChF,gBAAMM,IAAI,GAAG;AAAE9H,YAAAA,IAAF;AAAQrK,YAAAA,KAAR;AAAeG,YAAAA;AAAf,WAAb;AACAI,UAAAA,GAAG,CAACoE,eAAJ,CAAoBwN,IAAI,CAAC9H,IAAzB,EAA+BwH,MAA/B,EAAuCM,IAAI,CAACnS,KAA5C,EAAmDmS,IAAI,CAAChS,MAAxD;AACA,iBAAOgS,IAAP;AACH;;AAED1E,QAAAA,KAAK,CACDlN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD4O,OALC,EAMD0E,cANC,EAO8C;AAC/C,cAAI,CAACjO,aAAa,CAACyN,WAAnB,EAAgC;AAC5B,mBAAOQ,cAAP;AACH;;AAED,YAAEjO,aAAa,CAAC6D,eAAhB;AACA3D,UAAAA,MAAM,CAACF,aAAa,CAAC6D,eAAd,IAAiC,CAAlC,CAAN;AAEA,gBAAMsJ,KAAK,GAAGnN,aAAa,CAACuD,QAAd,CAAuB4J,KAArC;AACA,gBAAM3L,EAAE,GAAG7G,MAAM,CAACmO,MAAP,CAAcrF,cAAzB;;AAEA,kBAAQ0J,KAAK,CAACzO,IAAd;AACI,iBAAK;AAAA;AAAA,wCAAU2O,gBAAf;AAAiC;AAC7B,sBAAMa,QAAQ,GAAGf,KAAK,CAACG,oBAAvB;AACApN,gBAAAA,MAAM,CAAC,CAAC,CAACgO,QAAH,CAAN;AACA,uBAAO,KAAKC,+BAAL,CACH/R,GADG,EACE2D,UADF,EAEHC,aAFG,EAGHA,aAAa,CAACuD,QAHX,EAIH2K,QAJG,EAKH1M,EALG,EAMHxB,aAAa,CAACnE,KANX,EAOHmE,aAAa,CAAChE,MAPX,EAQHuN,OAAO,CAAC9I,SARL,CAAP;AASH;;AACD,iBAAK;AAAA;AAAA,wCAAU8M,YAAf;AAA6B;AACzB,sBAAMW,QAAQ,GAAGf,KAAK,CAACK,oBAAvB;AACAtN,gBAAAA,MAAM,CAAC,CAAC,CAACgO,QAAH,CAAN;AACA,uBAAO,KAAKE,2BAAL,CACHhS,GADG,EACE2D,UADF,EAEHC,aAFG,EAGHA,aAAa,CAACuD,QAHX,EAIH2K,QAJG,EAKH1M,EALG,EAMHxB,aAAa,CAACnE,KANX,EAOHmE,aAAa,CAAChE,MAPX,EAQHuN,OAAO,CAAC9I,SARL,CAAP;AASH;;AACD;AACI,qBAAOwN,cAAP;AA5BR;AA8BH;;AACOE,QAAAA,+BAA+B,CACnC/R,GADmC,EAEnC2D,UAFmC,EAGnCC,aAHmC,EAInCuD,QAJmC,EAKnC8K,aALmC,EAMnC7M,EANmC,EAOnC3F,KAPmC,EAQnCG,MARmC,EASnCsS,YATmC,EAUH;AAChC,gBAAMpN,SAAS,GAAGD,SAAS,CAACC,SAA5B,CADgC,CAEhC;AACA;AACA;AAEA;;AACA,gBAAM2M,UAAU,GAAGtK,QAAQ,CAAC4J,KAAT,CAAeU,UAAlC;AACA,gBAAMU,SAAS,GAAGV,UAAU,GAAG,CAA/B;AACA,eAAKjB,YAAL,CAAkBhL,MAAlB,GAA2B2M,SAA3B;AACA,eAAK1B,aAAL,CAAmBjL,MAAnB,GAA4B2M,SAA5B;AACA,eAAK3B,YAAL,CAAkB,CAAlB,IAAuB9Q,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWJ,KAAK,GAAG,CAAnB,CAAT,EAAgC,CAAhC,CAAvB;AACA,eAAKgR,aAAL,CAAmB,CAAnB,IAAwB/Q,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWD,MAAM,GAAG,CAApB,CAAT,EAAiC,CAAjC,CAAxB;;AACA,eAAK,IAAI0K,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAK6H,SAAtB,EAAiC,EAAE7H,CAAnC,EAAsC;AAClC,iBAAKkG,YAAL,CAAkBlG,CAAlB,IAAuB5K,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW,KAAK2Q,YAAL,CAAkBlG,CAAC,GAAG,CAAtB,IAA2B,CAAtC,CAAT,EAAmD,CAAnD,CAAvB;AACA,iBAAKmG,aAAL,CAAmBnG,CAAnB,IAAwB5K,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW,KAAK4Q,aAAL,CAAmBnG,CAAC,GAAG,CAAvB,IAA4B,CAAvC,CAAT,EAAoD,CAApD,CAAxB;AACH,WAhB+B,CAkBhC;;;AACA,eAAKoG,cAAL,CAAoBlL,MAApB,GAA6B2M,SAA7B;;AACA,eAAK,IAAI7H,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAK6H,SAAtB,EAAiC,EAAE7H,CAAnC,EAAsC;AAClC,iBAAKoG,cAAL,CAAoBpG,CAApB,IAA0B,WAAUlF,EAAG,IAAGkF,CAAE,EAA5C;AACH,WAtB+B,CAwBhC;;;AACA,eAAKgG,YAAL,CAAkBtN,CAAlB,GAAsBW,UAAU,CAACvC,cAAX,GAA4B,CAA5B,GAAgC,CAAtD;AACA,eAAKkP,YAAL,CAAkB3H,CAAlB,GAAsB,CAAtB,CA1BgC,CA0BP;;AACzB,eAAK2H,YAAL,CAAkB1H,CAAlB,GAAsBzB,QAAQ,CAAC4J,KAAT,CAAeqB,SAArC;AACA,eAAK9B,YAAL,CAAkBxR,CAAlB,GAAsBqI,QAAQ,CAAC4J,KAAT,CAAesB,eAAf,GAAiC,CAAjC,GAAqC,CAA3D,CA5BgC,CA8BhC;;AACA,gBAAMC,aAAa,GAAGtS,GAAG,CAACiE,aAAJ,CAAkB,KAAKuM,YAAL,CAAkB,CAAlB,CAAlB,EAAwC,KAAKC,aAAL,CAAmB,CAAnB,CAAxC,EAA+D,oBAA/D,CAAtB;AACA6B,UAAAA,aAAa,CAAClO,eAAd,CACI,KAAKsM,cAAL,CAAoB,CAApB,CADJ,EAEIpM,MAAM,CAACC,KAFX,EAGIC,OAAO,CAACC,KAHZ,EAII,KAAK4L,2BAJT;AAMAiC,UAAAA,aAAa,CAAC3N,UAAd,CAAyBuN,YAAzB,EAAuC,cAAvC;AACAI,UAAAA,aAAa,CAAClF,OAAd,CAAsB,aAAtB,EAAqC,KAAKkD,YAA1C;AACAgC,UAAAA,aAAa,CACR1N,QADL,CACcE,SAAS,CAACC,MADxB,EAEKC,iBAFL,CAEuBiN,aAFvB,EAEsC,CAFtC,EAxCgC,CA4ChC;;AACA,eAAK,IAAI3H,CAAC,GAAG,CAAb,EAAgBA,CAAC,KAAK6H,SAAtB,EAAiC,EAAE7H,CAAnC,EAAsC;AAClC,kBAAMiI,QAAQ,GAAGvS,GAAG,CAACiE,aAAJ,CAAkB,KAAKuM,YAAL,CAAkBlG,CAAlB,CAAlB,EAAwC,KAAKmG,aAAL,CAAmBnG,CAAnB,CAAxC,EAA+D,qBAA/D,CAAjB;AACAiI,YAAAA,QAAQ,CAACnO,eAAT,CAAyB,KAAKsM,cAAL,CAAoBpG,CAApB,CAAzB,EAAiDhG,MAAM,CAACC,KAAxD,EAA+DC,OAAO,CAACC,KAAvE,EAA8E,KAAK4L,2BAAnF;AACAkC,YAAAA,QAAQ,CAAC5N,UAAT,CAAoB,KAAK+L,cAAL,CAAoBpG,CAAC,GAAG,CAAxB,CAApB,EAAgD,cAAhD;AACA,iBAAKiG,aAAL,CAAmBvN,CAAnB,GAAuB,KAAKwN,YAAL,CAAkBlG,CAAC,GAAG,CAAtB,CAAvB;AACA,iBAAKiG,aAAL,CAAmB5H,CAAnB,GAAuB,KAAK8H,aAAL,CAAmBnG,CAAC,GAAG,CAAvB,CAAvB;AACAiI,YAAAA,QAAQ,CAACnF,OAAT,CAAiB,cAAjB,EAAiC,KAAKmD,aAAtC;AACAgC,YAAAA,QAAQ,CACH3N,QADL,CACcE,SAAS,CAACC,MADxB,EAEKC,iBAFL,CAEuBiN,aAFvB,EAEsC,CAFtC;AAGH,WAvD+B,CAyDhC;;;AACA,eAAK,IAAI3H,CAAC,GAAGmH,UAAb,EAAyBnH,CAAC,KAAK,CAA/B,GAAmC;AAC/B,kBAAMkI,MAAM,GAAGxS,GAAG,CAACiE,aAAJ,CAAkB,KAAKuM,YAAL,CAAkBlG,CAAlB,CAAlB,EAAwC,KAAKmG,aAAL,CAAmBnG,CAAnB,CAAxC,EAA+D,mBAA/D,CAAf;AACAkI,YAAAA,MAAM,CAACpO,eAAP,CAAuB,KAAKsM,cAAL,CAAoBpG,CAApB,CAAvB,EAA+ChG,MAAM,CAACC,KAAtD,EAA6DC,OAAO,CAACC,KAArE,EAA4E,KAAK4L,2BAAjF;AACAmC,YAAAA,MAAM,CAAC7N,UAAP,CAAkB,KAAK+L,cAAL,CAAoBpG,CAAC,GAAG,CAAxB,CAAlB,EAA8C,cAA9C;AACA,iBAAKiG,aAAL,CAAmBvN,CAAnB,GAAuB,KAAKwN,YAAL,CAAkBlG,CAAC,GAAG,CAAtB,CAAvB;AACA,iBAAKiG,aAAL,CAAmB5H,CAAnB,GAAuB,KAAK8H,aAAL,CAAmBnG,CAAC,GAAG,CAAvB,CAAvB;AACAkI,YAAAA,MAAM,CAACpF,OAAP,CAAe,cAAf,EAA+B,KAAKmD,aAApC;AACAiC,YAAAA,MAAM,CACD5N,QADL,CACcE,SAAS,CAACC,MADxB,EAEKC,iBAFL,CAEuBiN,aAFvB,EAEsC,CAFtC;AAGH,WApE+B,CAsEhC;;;AACA,eAAK3B,YAAL,CAAkBxR,CAAlB,GAAsBqI,QAAQ,CAAC4J,KAAT,CAAe0B,SAArC;AACA,gBAAMC,WAAW,GAAG1S,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,kBAAjC,CAApB;AACA8S,UAAAA,WAAW,CAACtO,eAAZ,CAA4B8N,YAA5B,EAA0C5N,MAAM,CAAC+G,IAAjD,EAAuD7G,OAAO,CAACC,KAA/D;AACAiO,UAAAA,WAAW,CAAC/N,UAAZ,CAAuB,KAAK+L,cAAL,CAAoB,CAApB,CAAvB,EAA+C,cAA/C;AACAgC,UAAAA,WAAW,CAACtF,OAAZ,CAAoB,aAApB,EAAmC,KAAKkD,YAAxC;AACAoC,UAAAA,WAAW,CACN9N,QADL,CACcE,SAAS,CAAC8E,KADxB,EAEK5E,iBAFL,CAEuBiN,aAFvB,EAEsC,CAFtC;;AAIA,cAAIrO,aAAa,CAAC6D,eAAd,KAAkC,CAAtC,EAAyC;AACrC,mBAAO/D,mBAAmB,CAAC1D,GAAD,EAAM2D,UAAN,EAAkBC,aAAlB,EAAiCsO,YAAjC,CAA1B;AACH,WAFD,MAEO;AACH,mBAAOQ,WAAP;AACH;AACJ;;AACOC,QAAAA,QAAQ,CACZ3S,GADY,EAEZP,KAFY,EAGZG,MAHY,EAIZgT,MAJY,EAKZvO,SALY,EAMZyN,QANY,EAOZe,SAPY,EAQZC,MAAkB,GAAGxO,MAAM,CAACC,KARhB,EASZwK,UAAqB,GAAGrK,2BATZ,EAUZqO,SAA8B,GAAGlO,SAAS,CAACC,SAAV,CAAoBC,MAVzC,EAUmF;AAC/F,gBAAMf,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiCgT,MAAjC,CAAb;AACA5O,UAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCyO,MAAhC,EAAwCtO,OAAO,CAACC,KAAhD,EAAuDsK,UAAvD;AACA/K,UAAAA,IAAI,CAACY,QAAL,CAAcmO,SAAd,EACK/N,iBADL,CACuB8M,QADvB,EACiCe,SADjC;AAEA,iBAAO7O,IAAP;AACH;;AACOgO,QAAAA,2BAA2B,CAC/BhS,GAD+B,EAE/B2D,UAF+B,EAG/BC,aAH+B,EAI/BuD,QAJ+B,EAK/B8K,aAL+B,EAM/B7M,EAN+B,EAO/B3F,KAP+B,EAQ/BG,MAR+B,EAS/BsS,YAT+B,EAUC;AAChC;AACA,eAAK5B,YAAL,CAAkBtN,CAAlB,GAAsBW,UAAU,CAACvC,cAAX,GAA4B,CAA5B,GAAgC,CAAtD;AACA,eAAKkP,YAAL,CAAkBtN,CAAlB,GAAsB,CAAtB,CAHgC,CAGP;;AACzB,eAAKsN,YAAL,CAAkB1H,CAAlB,GAAsBzB,QAAQ,CAAC4J,KAAT,CAAeqB,SAArC;AACA,eAAK9B,YAAL,CAAkBxR,CAAlB,GAAsBqI,QAAQ,CAAC4J,KAAT,CAAe0B,SAArC;AACA,gBAAMO,aAAa,GAAG,KAAKnC,iBAA3B,CANgC,CAQhC;;AACA,cAAIoC,cAAc,GAAG,KAAKN,QAAL,CACjB3S,GADiB,EAEjBgT,aAAa,CAACvT,KAFG,EAGjBuT,aAAa,CAACpT,MAHG,EAIjB,2BAJiB,EAKjBoT,aAAa,CAAClJ,IALG,EAMjBmI,aANiB,EAOjB,CAPiB,CAArB;;AASAgB,UAAAA,cAAc,CAACtO,UAAf,CAA0BuN,YAA1B,EAAwC,aAAxC;AACAe,UAAAA,cAAc,CAAC7F,OAAf,CAAuB,aAAvB,EAAsC,KAAKkD,YAA3C;AAEA,gBAAM4C,eAAe,GAAG,KAAKtC,wBAA7B,CArBgC,CAsBhC;;AACA,eAAK,IAAItG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4I,eAAe,CAAC1N,MAApC,EAA4C,EAAE8E,CAA9C,EAAiD;AAC7C,kBAAM6I,QAAQ,GAAGD,eAAe,CAAC5I,CAAD,CAAhC;AACA,kBAAM8I,UAAU,GAAG9I,CAAC,KAAK,CAAN,GAAU0I,aAAV,GAA0BE,eAAe,CAAC5I,CAAC,GAAG,CAAL,CAA5D;AACA,kBAAM+I,cAAc,GAAGD,UAAU,CAACtJ,IAAlC;AACA,iBAAKyG,aAAL,CAAmBvN,CAAnB,GAAuB,IAAIoQ,UAAU,CAAC3T,KAAtC;AACA,iBAAK8Q,aAAL,CAAmB5H,CAAnB,GAAuB,IAAIyK,UAAU,CAACxT,MAAtC;AACAqT,YAAAA,cAAc,GAAG,KAAKN,QAAL,CACb3S,GADa,EAEbmT,QAAQ,CAAC1T,KAFI,EAGb0T,QAAQ,CAACvT,MAHI,EAIb,4BAJa,EAKbuT,QAAQ,CAACrJ,IALI,EAMbmI,aANa,EAOb,CAPa,CAAjB;AASAgB,YAAAA,cAAc,CAACtO,UAAf,CAA0B0O,cAA1B,EAA0C,aAA1C;AACAJ,YAAAA,cAAc,CAAC7F,OAAf,CAAuB,aAAvB,EAAsC,KAAKmD,aAA3C;AACH;;AACD,gBAAM+C,SAAS,GAAGJ,eAAe,CAAC1N,MAAhB,GAAyB,CAA3C;AACA,gBAAM+N,aAAa,GAAG,KAAK5C,sBAA3B,CA1CgC,CA2ChC;;AACA,eAAK,IAAIrG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiJ,aAAa,CAAC/N,MAAlC,EAA0C8E,CAAC,EAA3C,EAA+C;AAC3C,kBAAM6I,QAAQ,GAAGI,aAAa,CAACjJ,CAAD,CAA9B;AACA,kBAAMkJ,SAAS,GAAGlJ,CAAC,KAAK,CAAN,GAAU4I,eAAe,CAACI,SAAD,CAAzB,GAAuCC,aAAa,CAACjJ,CAAC,GAAG,CAAL,CAAtE;AACA,kBAAMmJ,aAAa,GAAGD,SAAS,CAAC1J,IAAhC;AACA,iBAAKyG,aAAL,CAAmBvN,CAAnB,GAAuB,IAAIwQ,SAAS,CAAC/T,KAArC;AACA,iBAAK8Q,aAAL,CAAmB5H,CAAnB,GAAuB,IAAI6K,SAAS,CAAC5T,MAArC;AACAqT,YAAAA,cAAc,GAAG,KAAKN,QAAL,CACb3S,GADa,EAEbmT,QAAQ,CAAC1T,KAFI,EAGb0T,QAAQ,CAACvT,MAHI,EAIb,0BAJa,EAKbuT,QAAQ,CAACrJ,IALI,EAMbmI,aANa,EAOb,CAPa,CAAjB;AASAgB,YAAAA,cAAc,CAACtO,UAAf,CAA0B8O,aAA1B,EAAyC,aAAzC;AACAR,YAAAA,cAAc,CAACtO,UAAf,CAA0BuO,eAAe,CAACI,SAAS,GAAG,CAAZ,GAAgBhJ,CAAjB,CAAf,CAAmCR,IAA7D,EAAmE,mBAAnE;AACAmJ,YAAAA,cAAc,CAAC7F,OAAf,CAAuB,aAAvB,EAAsC,KAAKmD,aAA3C;AACH,WA9D+B,CAgEhC;;;AACA,gBAAMmC,WAAW,GAAG,KAAKC,QAAL,CAChB3S,GADgB,EAEhBP,KAFgB,EAGhBG,MAHgB,EAIhB,yBAJgB,EAKhBsS,YALgB,EAMhBD,aANgB,EAOhB,CAPgB,EAQhB3N,MAAM,CAAC+G,IARS,CAApB;;AAUAqH,UAAAA,WAAW,CAAC/N,UAAZ,CAAuB4O,aAAa,CAACA,aAAa,CAAC/N,MAAd,GAAuB,CAAxB,CAAb,CAAwCsE,IAA/D,EAAqE,cAArE;AACA4I,UAAAA,WAAW,CAACtF,OAAZ,CAAoB,aAApB,EAAmC,KAAKkD,YAAxC;;AACA,cAAI1M,aAAa,CAAC6D,eAAd,KAAkC,CAAtC,EAAyC;AACrC,mBAAO/D,mBAAmB,CAAC1D,GAAD,EAAM2D,UAAN,EAAkBC,aAAlB,EAAiCsO,YAAjC,CAA1B;AACH,WAFD,MAEO;AACH,mBAAOQ,WAAP;AACH;AACJ;;AA9UyE,O;;+CAoWjExU,6B,GAAN,MAAMA,6BAAN,CAA6E;AAAA;AAAA,eAoH/DwV,oBApH+D,GAoHxC,IAAIzN,IAAJ,CAAS,CAAT,EAAY,CAAZ,CApHwC;AAAA;;AAChF1C,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDE,QAAAA,cAAc,GAAW;AACrB,iBAAO,GAAP;AACH;;AACDoI,QAAAA,YAAY,CACRtN,MADQ,EAERoF,UAFQ,EAGRC,aAHQ,EAGqD;AAC7D,gBAAMuD,QAAQ,GAAGvD,aAAa,CAACuD,QAA/B;AAEAvD,UAAAA,aAAa,CAAC+P,kBAAd,GACMxM,QAAQ,CAACyM,YAAT,CAAsBjS,OAAtB,IACC,CAAC,CAACwF,QAAQ,CAACyM,YAAT,CAAsB9B,QADzB,IAEC,CAAC,CAAC3K,QAAQ,CAACyM,YAAT,CAAsBC,eAH/B;AAKAjQ,UAAAA,aAAa,CAACkQ,iBAAd,GACMlQ,aAAa,CAACgE,SAAd,CAAwB;AAAxB,aACChE,aAAa,CAAC+P,kBAFrB,CAR6D,CAUpB;;AAEzC,cAAI/P,aAAa,CAACkQ,iBAAlB,EAAqC;AACjC,cAAElQ,aAAa,CAAC6D,eAAhB;AACH;AACJ;;AACDgF,QAAAA,YAAY,CACRzM,GADQ,EAER2D,UAFQ,EAGRC,aAHQ,EAGqD;AAC7D,cAAIA,aAAa,CAAC+P,kBAAlB,EAAsC;AAClC7P,YAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACuD,QAAd,CAAuByM,YAAvB,CAAoC9B,QAAvC,CAAN;AACAlO,YAAAA,aAAa,CAACuD,QAAd,CAAuByM,YAAvB,CAAoC9B,QAApC,CAA6CiC,WAA7C,CACI,iBADJ,EAEInQ,aAAa,CAACuD,QAAd,CAAuByM,YAAvB,CAAoCC,eAFxC;AAGH;AACJ;;AACD3G,QAAAA,KAAK,CACDlN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD4O,OALC,EAMD0E,cANC,EAO8C;AAC/C,cAAI,CAACjO,aAAa,CAACkQ,iBAAnB,EAAsC;AAClC,mBAAOjC,cAAP;AACH;;AAED,YAAEjO,aAAa,CAAC6D,eAAhB;AACA3D,UAAAA,MAAM,CAACF,aAAa,CAAC6D,eAAd,IAAiC,CAAlC,CAAN;;AACA,cAAI7D,aAAa,CAAC6D,eAAd,KAAkC,CAAtC,EAAyC;AACrC,mBAAO,KAAKuM,sBAAL,CAA4BhU,GAA5B,EAAiC2D,UAAjC,EAA6CC,aAA7C,EACHA,aAAa,CAACnE,KADX,EACkBmE,aAAa,CAAChE,MADhC,EAEHuN,OAAO,CAAC9I,SAFL,EAEgBT,aAAa,CAACS,SAF9B,CAAP;AAGH,WAJD,MAIO;AACH,kBAAMe,EAAE,GAAGxB,aAAa,CAACyD,cAAzB;AACA,kBAAM4M,cAAc,GAAGrQ,aAAa,CAAC8D,kBAAd,GAChB,gBADgB,GAEhB,UAFP;AAIA,kBAAMwM,YAAY,GAAGjP,uBAAuB,CAACkI,OAAO,CAAC9I,SAAT,EAAoB4P,cAApB,EAAoC7O,EAApC,CAA5C;AACA,kBAAM8M,YAAY,GAAG/E,OAAO,CAAC9I,SAA7B;AACA8I,YAAAA,OAAO,CAAC9I,SAAR,GAAoB6P,YAApB;AAEA,mBAAO,KAAKF,sBAAL,CAA4BhU,GAA5B,EAAiC2D,UAAjC,EAA6CC,aAA7C,EACHA,aAAa,CAACnE,KADX,EACkBmE,aAAa,CAAChE,MADhC,EAEHsS,YAFG,EAEWgC,YAFX,CAAP;AAGH;AACJ;;AACOF,QAAAA,sBAAsB,CAC1BhU,GAD0B,EAE1B2D,UAF0B,EAG1BC,aAH0B,EAI1BnE,KAJ0B,EAK1BG,MAL0B,EAM1BsS,YAN0B,EAO1B7N,SAP0B,EAQM;AAChC,cAAIL,IAAJ;AACA,gBAAMmD,QAAQ,GAAGvD,aAAa,CAACuD,QAA/B;;AACA,cAAIvD,aAAa,CAAC+P,kBAAlB,EAAsC;AAClC7P,YAAAA,MAAM,CAAC,CAAC,CAACqD,QAAQ,CAACyM,YAAT,CAAsB9B,QAAzB,CAAN;AACAhO,YAAAA,MAAM,CAAC,CAAC,CAACqD,QAAQ,CAACyM,YAAT,CAAsBC,eAAzB,CAAN;AAEA,kBAAMM,MAAM,GAAGhN,QAAQ,CAACyM,YAAT,CAAsBC,eAArC;AACA,iBAAKH,oBAAL,CAA0B1Q,CAA1B,GAA8BmR,MAAM,CAAC1U,KAArC;AACA,iBAAKiU,oBAAL,CAA0B/K,CAA1B,GAA8BwL,MAAM,CAACvU,MAArC;AAEA,kBAAMwU,WAAW,GAAGD,MAAM,CAAC1U,KAAP,KAAiB0U,MAAM,CAACvU,MAA5C;;AACA,gBAAIwU,WAAJ,EAAiB;AACbpQ,cAAAA,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,sBAAjC,CAAP;AACH,aAFD,MAEO;AACHoE,cAAAA,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,sBAAjC,CAAP;AACH;;AACDoE,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CC,OAAO,CAACC,KAAtD,EAA6DC,2BAA7D;AACAV,YAAAA,IAAI,CAACW,UAAL,CAAgBuN,YAAhB,EAA8B,eAA9B;AACAlO,YAAAA,IAAI,CAACqQ,OAAL,CAAa,gBAAb,EAA+B,KAAKX,oBAApC;AACA1P,YAAAA,IAAI,CAACsQ,QAAL,CAAc,YAAd,EAA4BnN,QAAQ,CAACyM,YAAT,CAAsBW,UAAlD;AACAvQ,YAAAA,IAAI,CAACY,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoBC,MAAlC,EACKC,iBADL,CACuBmC,QAAQ,CAACyM,YAAT,CAAsB9B,QAD7C,EACuDsC,WAAW,GAAG,CAAH,GAAO,CADzE;AAEH,WApBD,MAoBO;AACHpQ,YAAAA,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,iBAAjC,CAAP;AACAoE,YAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CC,OAAO,CAACC,KAAtD,EAA6DC,2BAA7D;AACAV,YAAAA,IAAI,CAACW,UAAL,CAAgBuN,YAAhB,EAA8B,cAA9B;;AACA,gBAAI/K,QAAQ,CAACqN,WAAT,CAAqB1C,QAAzB,EAAmC;AAC/B9N,cAAAA,IAAI,CAACY,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoBC,MAAlC,EACKC,iBADL,CACuBmC,QAAQ,CAACqN,WAAT,CAAqB1C,QAD5C,EACsD,CADtD;AAEH,aAHD,MAGO;AACHhO,cAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACG,sBAAjB,CAAN;AACAC,cAAAA,IAAI,CAACY,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoBC,MAAlC,EACKC,iBADL,CACuBpB,aAAa,CAACG,sBADrC,EAC6D,CAD7D;AAEH;AACJ;;AACD,iBAAOC,IAAP;AACH;;AAnH+E,O;;wCA2HvE7F,sB,GAAN,MAAMA,sBAAN,CAAsE;AAAA;AA4FzE;AA5FyE,eA6FxDsW,WA7FwD,GA6F1C,IAAItO,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CA7F0C;AAAA;;AACzE5C,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDE,QAAAA,cAAc,GAAW;AACrB,iBAAO,GAAP;AACH;;AACDoI,QAAAA,YAAY,CACRtN,MADQ,EAERoF,UAFQ,EAGRC,aAHQ,EAG8C;AACtDA,UAAAA,aAAa,CAAC8Q,UAAd,GACM9Q,aAAa,CAACuD,QAAd,CAAuBwN,IAAvB,CAA4BhT,OAA5B,IACC,CAAC,CAACiC,aAAa,CAACuD,QAAd,CAAuBwN,IAAvB,CAA4B7C,QAFrC;;AAGA,cAAIlO,aAAa,CAAC8Q,UAAlB,EAA8B;AAC1B,cAAE9Q,aAAa,CAAC6D,eAAhB;AACH;AACJ;;AACDyF,QAAAA,KAAK,CACDlN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD4O,OALC,EAMD0E,cANC,EAO8C;AAC/C,cAAI,CAACjO,aAAa,CAAC8Q,UAAnB,EAA+B;AAC3B,mBAAO7C,cAAP;AACH;;AACD,YAAEjO,aAAa,CAAC6D,eAAhB;AACA3D,UAAAA,MAAM,CAACF,aAAa,CAAC6D,eAAd,IAAiC,CAAlC,CAAN;AAEA,gBAAMrC,EAAE,GAAGxB,aAAa,CAACyD,cAAzB;AACA,gBAAM4M,cAAc,GAAGrQ,aAAa,CAAC8D,kBAAd,GAChB,gBADgB,GAEhB,UAFP;AAGA,gBAAMwM,YAAY,GAAGjP,uBAAuB,CAACkI,OAAO,CAAC9I,SAAT,EAAoB4P,cAApB,EAAoC7O,EAApC,CAA5C;AAEAtB,UAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACuD,QAAd,CAAuBwN,IAAvB,CAA4B7C,QAA/B,CAAN;;AACA,cAAIlO,aAAa,CAAC6D,eAAd,KAAkC,CAAtC,EAAyC;AACrC,gBAAI7D,aAAa,CAAC8D,kBAAlB,EAAsC;AAClC,mBAAKkN,YAAL,CAAkB5U,GAAlB,EAAuB2D,UAAvB,EACIC,aAAa,CAACuD,QAAd,CAAuBwN,IAAvB,CAA4B7C,QADhC,EAEIlO,aAAa,CAACnE,KAFlB,EAGImE,aAAa,CAAChE,MAHlB,EAIIuN,OAAO,CAAC9I,SAJZ,EAKI6P,YALJ;;AAMA,qBAAOxQ,mBAAmB,CAAC1D,GAAD,EAAM2D,UAAN,EAAkBC,aAAlB,EAAiCsQ,YAAjC,CAA1B;AACH,aARD,MAQO;AACHpQ,cAAAA,MAAM,CAACF,aAAa,CAACnE,KAAd,KAAwBmE,aAAa,CAACM,WAAvC,CAAN;AACAJ,cAAAA,MAAM,CAACF,aAAa,CAAChE,MAAd,KAAyBgE,aAAa,CAACO,YAAxC,CAAN;AACA,qBAAO,KAAKyQ,YAAL,CAAkB5U,GAAlB,EAAuB2D,UAAvB,EACHC,aAAa,CAACuD,QAAd,CAAuBwN,IAAvB,CAA4B7C,QADzB,EAEHlO,aAAa,CAACnE,KAFX,EAGHmE,aAAa,CAAChE,MAHX,EAIHuN,OAAO,CAAC9I,SAJL,EAKHT,aAAa,CAACS,SALX,CAAP;AAMH;AACJ,WAnBD,MAmBO;AACH,kBAAMwQ,cAAc,GAAG1H,OAAO,CAAC9I,SAA/B;AACA8I,YAAAA,OAAO,CAAC9I,SAAR,GAAoB6P,YAApB;;AACA,kBAAMY,QAAQ,GAAG,KAAKF,YAAL,CAAkB5U,GAAlB,EAAuB2D,UAAvB,EACbC,aAAa,CAACuD,QAAd,CAAuBwN,IAAvB,CAA4B7C,QADf,EAEblO,aAAa,CAACnE,KAFD,EAGbmE,aAAa,CAAChE,MAHD,EAIbiV,cAJa,EAKbX,YALa,CAAjB;;AAMA,mBAAOY,QAAP;AACH;AACJ;;AACOF,QAAAA,YAAY,CAChB5U,GADgB,EAEhB2D,UAFgB,EAGhBoR,YAHgB,EAIhBtV,KAJgB,EAKhBG,MALgB,EAMhBsU,YANgB,EAOhB7P,SAPgB,EAQgB;AAChC,eAAKoQ,WAAL,CAAiBzR,CAAjB,GAAqBvD,KAArB;AACA,eAAKgV,WAAL,CAAiB9L,CAAjB,GAAqB/I,MAArB;AACA,eAAK6U,WAAL,CAAiB7L,CAAjB,GAAqB,IAAInJ,KAAzB;AACA,eAAKgV,WAAL,CAAiB3V,CAAjB,GAAqB,IAAIc,MAAzB;AAEA,gBAAMoE,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAb;AACAoE,UAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CC,OAAO,CAACC,KAAtD,EAA6DC,2BAA7D;AACAV,UAAAA,IAAI,CAACW,UAAL,CAAgBuP,YAAhB,EAA8B,eAA9B;AACAlQ,UAAAA,IAAI,CAACoJ,OAAL,CAAa,SAAb,EAAwB,KAAKqH,WAA7B;AACAzQ,UAAAA,IAAI,CAACY,QAAL,CAAcC,SAAS,CAACC,SAAV,CAAoBC,MAAlC,EACKC,iBADL,CACuB+P,YADvB,EACqC,CADrC;AAEA,iBAAO/Q,IAAP;AACH;;AA3FwE,O;;uCAoGhE5F,qB,GAAN,MAAMA,qBAAN,CAAqE;AAAA;AAgGxE;AAhGwE,eAiGvD4W,UAjGuD,GAiG1C,IAAI7O,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAjG0C;AAAA,eAkGvD8O,WAlGuD,GAkGzC,IAAI9O,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,EAAkB,CAAlB,CAlGyC;AAAA;;AACxE5C,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDE,QAAAA,cAAc,GAAW;AACrB,iBAAO,GAAP;AACH;;AACDoI,QAAAA,YAAY,CACRtN,MADQ,EAERoF,UAFQ,EAGRC,aAHQ,EAG6C;AACrD;AACAA,UAAAA,aAAa,CAACsR,SAAd,GAA0BtR,aAAa,CAACuD,QAAd,CAAuBgO,GAAvB,CAA2BxT,OAA3B,IACnB,CAAC,CAACiC,aAAa,CAACuD,QAAd,CAAuBgO,GAAvB,CAA2BrD,QADV,IAEnBlO,aAAa,CAAC8D,kBAFK,IAGnB9D,aAAa,CAAC+D,YAAd,GAA6B,GAHpC;;AAKA,cAAI/D,aAAa,CAACsR,SAAlB,EAA6B;AACzB,cAAEtR,aAAa,CAAC6D,eAAhB;AACH;AACJ;;AACDyF,QAAAA,KAAK,CACDlN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD4O,OALC,EAMD0E,cANC,EAO8C;AAC/C,cAAI,CAACjO,aAAa,CAACsR,SAAnB,EAA8B;AAC1B,mBAAOrD,cAAP;AACH;;AACD,YAAEjO,aAAa,CAAC6D,eAAhB;AAEA,gBAAMoN,cAAc,GAAG1H,OAAO,CAAC9I,SAA/B;AACA,gBAAM+Q,eAAe,GACfxR,aAAa,CAAC6D,eAAd,KAAkC,CAAlC,GACI7D,aAAa,CAACS,SADlB,GAEIY,uBAAuB,CAACkI,OAAO,CAAC9I,SAAT,EAAoB,SAApB,EAA+BT,aAAa,CAACyD,cAA7C,CAHjC;AAIA8F,UAAAA,OAAO,CAAC9I,SAAR,GAAoB+Q,eAApB;AAEAtR,UAAAA,MAAM,CAAC,CAAC,CAACF,aAAa,CAACuD,QAAd,CAAuBgO,GAAvB,CAA2BrD,QAA9B,CAAN;AACA,iBAAO,KAAKuD,WAAL,CAAiBrV,GAAjB,EAAsB2D,UAAtB,EAAkCC,aAAlC,EACHA,aAAa,CAACuD,QADX,EAEHvD,aAAa,CAACuD,QAAd,CAAuBgO,GAAvB,CAA2BrD,QAFxB,EAGHlO,aAAa,CAACyD,cAHX,EAIHzD,aAAa,CAACnE,KAJX,EAKHmE,aAAa,CAAChE,MALX,EAMHiV,cANG,EAOHjR,aAAa,CAACM,WAPX,EAQHN,aAAa,CAACO,YARX,EASHiR,eATG,CAAP;AAUH;;AACOC,QAAAA,WAAW,CACfrV,GADe,EAEf2D,UAFe,EAGfC,aAHe,EAIfuD,QAJe,EAKfmO,WALe,EAMflQ,EANe,EAOf3F,KAPe,EAQfG,MARe,EASfiV,cATe,EAUf3Q,WAVe,EAWfC,YAXe,EAYfiR,eAZe,EAaiB;AAChC,eAAKH,WAAL,CAAiBjS,CAAjB,GAAqBvD,KAArB;AACA,eAAKwV,WAAL,CAAiBtM,CAAjB,GAAqB/I,MAArB;AACA,eAAKqV,WAAL,CAAiBrM,CAAjB,GAAqB1E,WAArB;AACA,eAAK+Q,WAAL,CAAiBnW,CAAjB,GAAqBqF,YAArB;AACA,eAAK6Q,UAAL,CAAgBhS,CAAhB,GAAoB4C,KAAK,CAAC,MAAMuB,QAAQ,CAACgO,GAAT,CAAaI,SAApB,EAA+B,IAA/B,EAAqC,IAArC,CAAzB;AAEA,gBAAMC,aAAa,GAAG,SAAtB;AAEA,gBAAMC,YAAY,GAAGxQ,uBAAuB,CAACmQ,eAAD,EAAkBI,aAAlB,EAAiCpQ,EAAjC,CAA5C;AAEA,gBAAMsQ,QAAQ,GAAG1V,GAAG,CAACiE,aAAJ,CAAkBC,WAAlB,EAA+BC,YAA/B,EAA6C,aAA7C,CAAjB;AACAuR,UAAAA,QAAQ,CAACtR,eAAT,CAAyBqR,YAAzB,EAAuCnR,MAAM,CAACC,KAA9C,EAAqDC,OAAO,CAACC,KAA7D,EAAoEC,2BAApE;AACAgR,UAAAA,QAAQ,CAAC/Q,UAAT,CAAoBkQ,cAApB,EAAoC,iBAApC;AACAa,UAAAA,QAAQ,CAACtI,OAAT,CAAiB,YAAjB,EAA+B,KAAK6H,WAApC;AACAS,UAAAA,QAAQ,CACH9Q,QADL,CACcC,SAAS,CAACC,SAAV,CAAoBC,MADlC,EAEKC,iBAFL,CAEuBsQ,WAFvB,EAEoC,CAFpC;AAIA,gBAAMK,QAAQ,GAAG3V,GAAG,CAACiE,aAAJ,CAAkBC,WAAlB,EAA+BC,YAA/B,EAA6C,aAA7C,CAAjB;AACAwR,UAAAA,QAAQ,CAACvR,eAAT,CAAyBgR,eAAzB,EAA0C9Q,MAAM,CAACC,KAAjD,EAAwDC,OAAO,CAACC,KAAhE,EAAuEC,2BAAvE;AACAiR,UAAAA,QAAQ,CAAChR,UAAT,CAAoB8Q,YAApB,EAAkC,iBAAlC;AACAE,UAAAA,QAAQ,CAACvI,OAAT,CAAiB,YAAjB,EAA+B,KAAK6H,WAApC;AACAU,UAAAA,QAAQ,CAACvI,OAAT,CAAiB,WAAjB,EAA8B,KAAK4H,UAAnC;AACAW,UAAAA,QAAQ,CACH/Q,QADL,CACcC,SAAS,CAACC,SAAV,CAAoBC,MADlC,EAEKC,iBAFL,CAEuBsQ,WAFvB,EAEoC,CAFpC;AAIA,iBAAOK,QAAP;AACH;;AA/FuE,O;;sCAqG/DtX,oB,GAAN,MAAMA,oBAAN,CAAoE;AACvEkF,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDE,QAAAA,cAAc,GAAW;AACrB,iBAAO,IAAP;AACH;;AACDyJ,QAAAA,KAAK,CACDlN,GADC,EAED2D,UAFC,EAGDC,aAHC,EAIDrF,MAJC,EAKD4O,OALC,EAMD0E,cANC,EAO8C;AAC/C/N,UAAAA,MAAM,CAAC,CAAC,CAAC+N,cAAH,CAAN;AAEA,cAAI+D,KAAK,GAAG/Q,SAAS,CAACsF,UAAV,CAAqB0L,EAAjC;;AACA,cAAIjS,aAAa,CAAC4D,cAAlB,EAAkC;AAC9BoO,YAAAA,KAAK,IAAI/Q,SAAS,CAACsF,UAAV,CAAqB2L,QAA9B;AACAjE,YAAAA,cAAc,CAACkE,cAAf,GAAgC,IAAhC;AACH;;AACDlE,UAAAA,cAAc,CACTjN,QADL,CACcC,SAAS,CAACC,SAAV,CAAoB8E,KADlC,EACyC,SADzC,EACoD,SADpD,EAEKM,QAFL,CAEc3L,MAFd,EAEsBqX,KAFtB;AAIA,iBAAO/D,cAAP;AACH;;AA3BsE,O;;AA8B3E,UAAIhN,SAAJ,EAAe;AAAA,SAEL;AAAEC,UAAAA,SAAF;AAAaqF,UAAAA;AAAb,SAFK,GAEuBtF,SAFvB;;AAIX,cAAMmR,sBAAN,CAAkE;AAAA;AAAA,iBAC7CC,cAD6C,GACJtQ,QAAQ,CAACuQ,QAAT,CAAkBC,IAAlB,CAAuBC,aADnB;AAAA,iBAE7CC,YAF6C,GAE9B,IAAIrY,yBAAJ,EAF8B;AAAA,iBAG7CsY,UAH6C,GAGhC,IAAIrY,uBAAJ,EAHgC;AAAA,iBAI7CsY,gBAJ6C,GAI1B,IAAIrY,6BAAJ,EAJ0B;AAAA,iBAK7CsY,SAL6C,GAKjC,IAAIrY,sBAAJ,EALiC;AAAA,iBAM7CsY,QAN6C,GAMlC,IAAIrY,qBAAJ,EANkC;AAAA,iBAO7CsY,OAP6C,GAOnC,IAAIrY,oBAAJ,EAPmC;AAQ9D;AAR8D,iBAS7CoN,WAT6C,GAS/B,IAAI7E,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,CAT+B;AAAA,iBAU7C4E,SAV6C,GAUjC,IAAI1E,QAAJ,EAViC;AAAA,iBAW7C6P,QAX6C,GAWlC,IAAI9Y,eAAJ,EAXkC;AAAA,iBAY7C+Y,cAZ6C,GAY5B,IAAI9Y,aAAJ,EAZ4B;AAa9D;AAb8D,iBAc7C+Y,uBAd6C,GAcnB,IAAI9Q,QAAJ,EAdmB;AAgB9D;AAhB8D,iBAiBtD+Q,YAjBsD,GAiBvC,KAjBuC;AAiBhC;AAjBgC,iBAkBtDC,aAlBsD,GAkBL,EAlBK;AAAA;;AAoBtDC,UAAAA,qBAAqB,CACzBzY,MADyB,EAEzBqF,aAFyB,EAEK;AAC9B,kBAAMqT,YAAqB,GACrB1Y,MAAM,CAAC4N,WAAP,KAAuBpF,WAAW,CAACqF,UAAnC,IACC7N,MAAM,CAAC4N,WAAP,KAAuBpF,WAAW,CAACmQ,OAF1C;;AAIA,gBAAID,YAAJ,EAAkB;AACd,oBAAME,cAAc,GAAGtS,SAAS,CAACuS,yBAAV,EAAvB;;AACA,kBAAID,cAAJ,EAAoB;AAChBvT,gBAAAA,aAAa,CAACuD,QAAd,GAAyBgQ,cAAzB;AACH,eAFD,MAEO;AACHvT,gBAAAA,aAAa,CAACuD,QAAd,GAAyBD,eAAzB;AACH;AACJ,aAPD,MAOO;AACH,kBAAI3I,MAAM,CAAC8Y,gBAAX,EAA6B;AACzBzT,gBAAAA,aAAa,CAACuD,QAAd,GAAyB5I,MAAM,CAAC8Y,gBAAhC;AACH,eAFD,MAEO;AACHzT,gBAAAA,aAAa,CAACuD,QAAd,GAAyBD,eAAzB;AACH;AACJ;AACJ;;AAEOoQ,UAAAA,sBAAsB,CAAC1T,aAAD,EAAqC;AAC/D,kBAAMT,YAAY,GAAG,KAAK4T,aAA1B;AACA5T,YAAAA,YAAY,CAACqC,MAAb,GAAsB,CAAtB;AAEA,kBAAM2B,QAAQ,GAAGvD,aAAa,CAACuD,QAA/B;;AACA,gBAAIA,QAAQ,CAACoQ,OAAb,EAAsB;AAClB,mBAAK,MAAMvT,IAAX,IAAmBmD,QAAQ,CAACoQ,OAA5B,EAAqC;AACjCpU,gBAAAA,YAAY,CAAC4F,IAAb,CAAkB/E,IAAlB;AACH;;AACDF,cAAAA,MAAM,CAACX,YAAY,CAACqC,MAAb,KAAwB2B,QAAQ,CAACoQ,OAAT,CAAiB/R,MAA1C,CAAN;AACH;;AAEDrC,YAAAA,YAAY,CAAC4F,IAAb,CAAkB,KAAKsN,YAAvB;;AAEA,gBAAIlP,QAAQ,CAAC4J,KAAT,CAAepP,OAAnB,EAA4B;AACxBwB,cAAAA,YAAY,CAAC4F,IAAb,CAAkB,KAAKuN,UAAvB;AACH;;AAEDnT,YAAAA,YAAY,CAAC4F,IAAb,CAAkB,KAAKwN,gBAAvB;;AAEA,gBAAIpP,QAAQ,CAACwN,IAAT,CAAchT,OAAlB,EAA2B;AACvBwB,cAAAA,YAAY,CAAC4F,IAAb,CAAkB,KAAKyN,SAAvB;AACH;;AAED,gBAAIrP,QAAQ,CAACgO,GAAT,CAAaxT,OAAjB,EAA0B;AACtBwB,cAAAA,YAAY,CAAC4F,IAAb,CAAkB,KAAK0N,QAAvB;AACH;;AACDtT,YAAAA,YAAY,CAAC4F,IAAb,CAAkB,KAAK2N,OAAvB;AACH;;AAEOc,UAAAA,0BAA0B,CAC9BxX,GAD8B,EAE9BzB,MAF8B,EAG9BuN,eAH8B,EAI9BlI,aAJ8B,EAKhC;AACE,kBAAM8I,MAAM,GAAGnO,MAAM,CAACmO,MAAtB;AACA,kBAAMtF,gBAAyB,GAAG7I,MAAM,CAAC4N,WAAP,KAAuBpF,WAAW,CAAC0Q,IAAnC,IAA2C,CAAC,CAAC/K,MAAM,CAACgL,SAAtF;AACA,kBAAMC,UAAU,GAAGvQ,gBAAgB,IAAI7I,MAAM,CAAC4N,WAAP,KAAuBpF,WAAW,CAACsF,SAA1E,CAHF,CAKE;;AACAzI,YAAAA,aAAa,CAACwD,gBAAd,GAAiCA,gBAAjC;AACAxD,YAAAA,aAAa,CAACyD,cAAd,GAA+BqF,MAAM,CAACrF,cAAtC,CAPF,CASE;;AACAzD,YAAAA,aAAa,CAACS,SAAd,GAA0BqI,MAAM,CAACrI,SAAjC;AACAT,YAAAA,aAAa,CAAC0D,gBAAd,GAAiCoF,MAAM,CAACpF,gBAAxC,CAXF,CAaE;;AACA1D,YAAAA,aAAa,CAAC2D,kBAAd,GAAmC,CAAChJ,MAAM,CAACqZ,UAAP,GAAqB9R,MAAM,CAAC+R,IAAP,CAAYC,OAAlC,MAAgD,CAAnF;AACAlU,YAAAA,aAAa,CAAC4D,cAAd,GAA+BxH,GAAG,CAAC+X,QAAJ,IAAgBJ,UAA/C;AACA/T,YAAAA,aAAa,CAAC6D,eAAd,GAAgC,CAAhC,CAhBF,CAkBE;;AACA7D,YAAAA,aAAa,CAAC+D,YAAd,GAA6B/D,aAAa,CAACuD,QAAd,CAAuBQ,YAApD;AACA/D,YAAAA,aAAa,CAAC8D,kBAAd,GAAmC9D,aAAa,CAACuD,QAAd,CAAuBO,kBAAvB,IAC5B9D,aAAa,CAAC+D,YAAd,KAA+B,GADtC;AAGA/D,YAAAA,aAAa,CAACM,WAAd,GAA4BxE,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW6M,MAAM,CAACjN,KAAlB,CAAT,EAAmC,CAAnC,CAA5B;AACAmE,YAAAA,aAAa,CAACO,YAAd,GAA6BzE,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW6M,MAAM,CAAC9M,MAAlB,CAAT,EAAoC,CAApC,CAA7B;AAEAgE,YAAAA,aAAa,CAACnE,KAAd,GAAsBmE,aAAa,CAAC8D,kBAAd,GAChBhI,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW+D,aAAa,CAACM,WAAd,GAA4BN,aAAa,CAAC+D,YAArD,CAAT,EAA6E,CAA7E,CADgB,GAEhB/D,aAAa,CAACM,WAFpB;AAGAN,YAAAA,aAAa,CAAChE,MAAd,GAAuBgE,aAAa,CAAC8D,kBAAd,GACjBhI,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAW+D,aAAa,CAACO,YAAd,GAA6BP,aAAa,CAAC+D,YAAtD,CAAT,EAA8E,CAA9E,CADiB,GAEjB/D,aAAa,CAACO,YAFpB,CA7BF,CAiCE;;AACAP,YAAAA,aAAa,CAACgE,SAAd,GAA0BhE,aAAa,CAAC2D,kBAAd,IACnBuE,eAAe,CAAC1K,cADvB;AAEAwC,YAAAA,aAAa,CAACiE,cAAd,GAA+BjE,aAAa,CAACgE,SAAd,GACzBhH,GAAG,CAACmB,MAAJ,CAAWiW,OADc,GACJpX,GAAG,CAACmB,MAAJ,CAAWE,KADtC,CApCF,CAuCE;;AACA2B,YAAAA,aAAa,CAACG,sBAAd,GAAuC,KAAK8S,uBAA5C,CAxCF,CA0CE;;AACAjT,YAAAA,aAAa,CAACkE,qBAAd,GAAsC,KAAtC;AACH;;AAEOmQ,UAAAA,mBAAmB,CACvBjY,GADuB,EAEvBzB,MAFuB,EAGvBuN,eAHuB,EAIvBlI,aAJuB,EAKnB;AACJ,iBAAKoT,qBAAL,CAA2BzY,MAA3B,EAAmCqF,aAAnC;;AAEA,iBAAK0T,sBAAL,CAA4B1T,aAA5B;;AAEAV,YAAAA,qCAAqC,CAAC,KAAK6T,aAAN,CAArC;;AAEA,iBAAKS,0BAAL,CAAgCxX,GAAhC,EAAqCzB,MAArC,EAA6CuN,eAA7C,EAA8DlI,aAA9D;;AAEA,iBAAK,MAAMsU,OAAX,IAAsB,KAAKnB,aAA3B,EAA0C;AACtC,kBAAImB,OAAO,CAACrM,YAAZ,EAA0B;AACtBqM,gBAAAA,OAAO,CAACrM,YAAR,CAAqBtN,MAArB,EAA6BuN,eAA7B,EAA8ClI,aAA9C;AACH;AACJ;AACJ,WA/I6D,CAiJ9D;AACA;AACA;;;AACA6I,UAAAA,YAAY,CACRzM,GADQ,EAER0M,MAFQ,EAGRnO,MAHQ,EAIR2F,WAJQ,EAKRC,YALQ,EAMJ;AACJpE,YAAAA,oBAAoB,CAACC,GAAD,EAAM,KAAK2W,QAAX,CAApB;;AAEA,iBAAKsB,mBAAL,CAAyBjY,GAAzB,EAA8BzB,MAA9B,EAAsC,KAAKoY,QAA3C,EAAqD,KAAKC,cAA1D,EAHI,CAKJ;;;AACA,kBAAMxR,EAAE,GAAGsH,MAAM,CAACrF,cAAlB;AAEArH,YAAAA,GAAG,CAACwO,eAAJ,CAAoB,KAAKoI,cAAL,CAAoBvS,SAAxC,EACItC,MAAM,CAACE,KADX,EACkBiC,WADlB,EAC+BC,YAD/B,EAC6CuI,MAD7C,EAEI,KAAKkK,cAAL,CAAoBtP,gBAFxB;AAIA,kBAAM7H,KAAK,GAAG,KAAKmX,cAAL,CAAoBnX,KAAlC;AACA,kBAAMG,MAAM,GAAG,KAAKgX,cAAL,CAAoBhX,MAAnC;;AAEA,gBAAI,KAAKgX,cAAL,CAAoBlP,kBAAxB,EAA4C;AACxC1H,cAAAA,GAAG,CAACwK,eAAJ,CAAqB,oBAAmBpF,EAAG,EAA3C,EAA8CrD,MAAM,CAACe,aAArD,EAAoErD,KAApE,EAA2EG,MAA3E;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,mBAAkBgB,EAAG,EAA1C,EAA6C,KAAKwR,cAAL,CAAoB/O,cAAjE,EAAiFpI,KAAjF,EAAwFG,MAAxF;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,mBAAkBgB,EAAG,EAA1C,EAA6C,KAAKwR,cAAL,CAAoB/O,cAAjE,EAAiFpI,KAAjF,EAAwFG,MAAxF;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,mBAAkBgB,EAAG,EAA1C,EAA6CrD,MAAM,CAACE,KAApD,EAA2DxC,KAA3D,EAAkEG,MAAlE;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,mBAAkBgB,EAAG,EAA1C,EAA6CrD,MAAM,CAACE,KAApD,EAA2DxC,KAA3D,EAAkEG,MAAlE;AACH,aAND,MAMO;AACHI,cAAAA,GAAG,CAACwK,eAAJ,CAAqB,cAAapF,EAAG,EAArC,EAAwCrD,MAAM,CAACe,aAA/C,EAA8DrD,KAA9D,EAAqEG,MAArE;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,aAAYgB,EAAG,EAApC,EAAuC,KAAKwR,cAAL,CAAoB/O,cAA3D,EAA2EpI,KAA3E,EAAkFG,MAAlF;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,aAAYgB,EAAG,EAApC,EAAuC,KAAKwR,cAAL,CAAoB/O,cAA3D,EAA2EpI,KAA3E,EAAkFG,MAAlF;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,aAAYgB,EAAG,EAApC,EAAuCrD,MAAM,CAACE,KAA9C,EAAqDxC,KAArD,EAA4DG,MAA5D;AACAI,cAAAA,GAAG,CAACoE,eAAJ,CAAqB,aAAYgB,EAAG,EAApC,EAAuCrD,MAAM,CAACE,KAA9C,EAAqDxC,KAArD,EAA4DG,MAA5D;AACH;;AACDI,YAAAA,GAAG,CAACoE,eAAJ,CAAqB,YAAWgB,EAAG,EAAnC,EAAsCrD,MAAM,CAACE,KAA7C,EAAoDiC,WAApD,EAAiEC,YAAjE;AACAnE,YAAAA,GAAG,CAACoE,eAAJ,CAAqB,YAAWgB,EAAG,EAAnC,EAAsCrD,MAAM,CAACE,KAA7C,EAAoDiC,WAApD,EAAiEC,YAAjE;;AAEA,iBAAK,MAAM+T,OAAX,IAAsB,KAAKnB,aAA3B,EAA0C;AACtC,kBAAImB,OAAO,CAACzL,YAAZ,EAA0B;AACtByL,gBAAAA,OAAO,CAACzL,YAAR,CAAqBzM,GAArB,EAA0B,KAAK2W,QAA/B,EAAyC,KAAKC,cAA9C,EAA8DlK,MAA9D,EAAsEnO,MAAtE,EAA8E2F,WAA9E,EAA2FC,YAA3F;AACH;AACJ;AACJ;;AACD+I,UAAAA,KAAK,CAACiL,OAAD,EAAmCnY,GAAnC,EAAuE;AACxE;AACA,gBAAI,KAAKoY,cAAL,CAAoBpY,GAApB,CAAJ,EAA8B;AAC1B;AACH,aAJuE,CAMxE;AACA;;;AACA,iBAAK,MAAMzB,MAAX,IAAqB4Z,OAArB,EAA8B;AAC1B;AACA,kBAAI,CAAC5Z,MAAM,CAACiE,KAAR,IAAiB,CAACjE,MAAM,CAACmO,MAA7B,EAAqC;AACjC;AACH,eAJyB,CAK1B;;;AACA,mBAAKuL,mBAAL,CAAyBjY,GAAzB,EAA8BzB,MAA9B,EAAsC,KAAKoY,QAA3C,EAAqD,KAAKC,cAA1D,EAN0B,CAO1B;AACA;;;AAEA,mBAAKX,cAAL,CAAoBoC,IAApB,CAAyBrS,iBAAiB,CAACsS,mBAA3C,EAAgE/Z,MAAhE,EAV0B,CAY1B;;;AACA,kBAAI,KAAKqY,cAAL,CAAoBrP,kBAAxB,EAA4C;AACxC,qBAAKgR,qBAAL,CAA2BvY,GAA3B,EAAgCzB,MAAhC,EAAwCA,MAAM,CAACiE,KAA/C,EAAsD,KAAKuU,aAA3D;AACH,eAFD,MAEO;AACH,qBAAKyB,oBAAL,CAA0BxY,GAA1B,EAA+BzB,MAA/B;AACH;;AAED,mBAAK0X,cAAL,CAAoBoC,IAApB,CAAyBrS,iBAAiB,CAACyS,iBAA3C,EAA8Dla,MAA9D;AACH;AACJ,WA5N6D,CA6N9D;AACA;AACA;;;AACQia,UAAAA,oBAAoB,CACxBxY,GADwB,EAExBzB,MAFwB,EAGpB;AACJ,kBAAMkB,KAAK,GAAGC,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWtB,MAAM,CAACmO,MAAP,CAAcjN,KAAzB,CAAT,EAA0C,CAA1C,CAAd;AACA,kBAAMG,MAAM,GAAGF,IAAI,CAACI,GAAL,CAASJ,IAAI,CAACG,KAAL,CAAWtB,MAAM,CAACmO,MAAP,CAAc9M,MAAzB,CAAT,EAA2C,CAA3C,CAAf;AACA,kBAAMyE,SAAS,GAAG,KAAKuS,cAAL,CAAoBvS,SAAtC;AACA,kBAAMiD,gBAAgB,GAAG,KAAKsP,cAAL,CAAoBtP,gBAA7C;AAEA,kBAAM2D,QAAQ,GAAG1M,MAAM,CAAC0M,QAAxB,CANI,CAM+B;;AACnC,iBAAKO,SAAL,CAAejM,IAAf,GAAsBG,IAAI,CAAC4P,KAAL,CAAWrE,QAAQ,CAACjI,CAAT,GAAavD,KAAxB,CAAtB;AACA,iBAAK+L,SAAL,CAAehM,GAAf,GAAqBE,IAAI,CAAC4P,KAAL,CAAWrE,QAAQ,CAACtC,CAAT,GAAa/I,MAAxB,CAArB,CARI,CASJ;AACA;;AACA,iBAAK4L,SAAL,CAAe/L,KAAf,GAAuBC,IAAI,CAACI,GAAL,CAASJ,IAAI,CAAC4P,KAAL,CAAWrE,QAAQ,CAACxL,KAAT,GAAiBA,KAA5B,CAAT,EAA6C,CAA7C,CAAvB;AACA,iBAAK+L,SAAL,CAAe5L,MAAf,GAAwBF,IAAI,CAACI,GAAL,CAASJ,IAAI,CAAC4P,KAAL,CAAWrE,QAAQ,CAACrL,MAAT,GAAkBA,MAA7B,CAAT,EAA+C,CAA/C,CAAxB;AAEA,kBAAMmP,UAAU,GAAGxQ,MAAM,CAACwQ,UAA1B,CAdI,CAcmC;;AACvC,iBAAKtD,WAAL,CAAiBzI,CAAjB,GAAqB+L,UAAU,CAAC/L,CAAhC;AACA,iBAAKyI,WAAL,CAAiB9C,CAAjB,GAAqBoG,UAAU,CAACpG,CAAhC;AACA,iBAAK8C,WAAL,CAAiB7C,CAAjB,GAAqBmG,UAAU,CAACnG,CAAhC;AACA,iBAAK6C,WAAL,CAAiB3M,CAAjB,GAAqBiQ,UAAU,CAACjQ,CAAhC;AAEA,kBAAMkF,IAAI,GAAGhE,GAAG,CAACiE,aAAJ,CAAkBxE,KAAlB,EAAyBG,MAAzB,EAAiC,SAAjC,CAAb,CApBI,CAsBJ;;AACA,gBAAItB,qBAAqB,CAACC,MAAD,CAAzB,EAAmC;AAC/ByF,cAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAACC,KAAvC,EAA8CC,OAAO,CAACC,KAAtD,EAA6D,KAAKgH,WAAlE;AACH,aAFD,MAEO;AACHzH,cAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB,EAAgCC,MAAM,CAAC+G,IAAvC,EAA6C7G,OAAO,CAACC,KAArD;AACH,aA3BG,CA6BJ;;;AACA,gBAAIlG,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aAApC,EAAmD;AAC/CkB,cAAAA,IAAI,CAACwG,eAAL,CACIlD,gBADJ,EAEIhD,MAAM,CAACC,KAFX,EAGIC,OAAO,CAACiG,OAHZ,EAIIlM,MAAM,CAAC0Q,UAJX,EAKI1Q,MAAM,CAAC2Q,YALX,EAMI3Q,MAAM,CAACC,SAAP,GAAmBC,YAAY,CAACqE,aANpC;AAQH,aATD,MASO;AACHkB,cAAAA,IAAI,CAACwG,eAAL,CAAqBlD,gBAArB,EAAuChD,MAAM,CAAC+G,IAA9C,EAAoD7G,OAAO,CAACiG,OAA5D;AACH;;AAEDzG,YAAAA,IAAI,CAACoH,WAAL,CAAiB,KAAKI,SAAtB,EA3CI,CA6CJ;;AACAxH,YAAAA,IAAI,CAACY,QAAL,CAAcE,SAAS,CAACC,MAAxB,EACKmF,QADL,CACc3L,MADd,EACsB4L,UAAU,CAACpF,MADjC,EA9CI,CAiDJ;;AACA,gBAAI6Q,KAAK,GAAGzL,UAAU,CAACP,KAAX,GAAmBO,UAAU,CAAC0L,EAA1C;;AACA,gBAAI,KAAKe,cAAL,CAAoBpP,cAAxB,EAAwC;AACpCoO,cAAAA,KAAK,IAAIzL,UAAU,CAAC2L,QAApB;AACA9R,cAAAA,IAAI,CAAC+R,cAAL,GAAsB,IAAtB;AACH;;AACD/R,YAAAA,IAAI,CAACY,QAAL,CAAcE,SAAS,CAAC8E,KAAxB,EACKM,QADL,CACc3L,MADd,EACsBqX,KADtB;AAEH;;AAEO2C,UAAAA,qBAAqB,CACzBvY,GADyB,EAEzBzB,MAFyB,EAGzBiE,KAHyB,EAIzBW,YAJyB,EAKrB;AACJK,YAAAA,qCAAqC,CAACL,YAAD,CAArC;AAEA,kBAAMgK,OAAwB,GAAG;AAC7B9I,cAAAA,SAAS,EAAE,EADkB;AAE7BiD,cAAAA,gBAAgB,EAAE;AAFW,aAAjC;AAKA,gBAAIwN,QAAsD,GAAG1F,SAA7D;;AAEA,iBAAK,MAAM8I,OAAX,IAAsB/U,YAAtB,EAAoC;AAChC,kBAAI+U,OAAO,CAAChL,KAAZ,EAAmB;AACf4H,gBAAAA,QAAQ,GAAGoD,OAAO,CAAChL,KAAR,CAAclN,GAAd,EAAmB,KAAK2W,QAAxB,EAAkC,KAAKC,cAAvC,EACPrY,MADO,EACC4O,OADD,EACU2H,QADV,CAAX;AAEH;AACJ;;AAEDhR,YAAAA,MAAM,CAAC,KAAK8S,cAAL,CAAoBnP,eAApB,KAAwC,CAAzC,CAAN;AACH;;AAEO2Q,UAAAA,cAAc,CAACpY,GAAD,EAAuC;AACzD,gBAAI,KAAK8W,YAAT,EAAuB;AACnB,qBAAO,CAAP;AACH;;AAED/W,YAAAA,oBAAoB,CAACC,GAAD,EAAM,KAAK2W,QAAX,CAApB,CALyD,CAOzD;;AACA,iBAAKE,uBAAL,CAA6B6B,KAA7B,GAAsC,wCAAtC;;AACA,iBAAK7B,uBAAL,CAA6B8B,UAA7B,CAAwC;AAAEC,cAAAA,UAAU,EAAE;AAAd,aAAxC;;AAEA,gBAAI,KAAK/B,uBAAL,CAA6BgC,WAAjC,EAA8C;AAC1C,mBAAK/B,YAAL,GAAoB,IAApB;AACH;;AAED,mBAAO,KAAKA,YAAL,GAAoB,CAApB,GAAwB,CAA/B;AACH;;AAvU6D;;AA0UlEjS,QAAAA,SAAS,CAACiU,iBAAV,CAA4B,SAA5B,EAAuC,IAAI9C,sBAAJ,EAAvC;AAEH,O,CAAC", "sourcesContent": ["/*\r\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\r\n\r\n https://www.cocos.com/\r\n\r\n Permission is hereby granted, free of charge, to any person obtaining a copy\r\n of this software and associated documentation files (the \"Software\"), to deal\r\n in the Software without restriction, including without limitation the rights to\r\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\r\n of the Software, and to permit persons to whom the Software is furnished to do so,\r\n subject to the following conditions:\r\n\r\n The above copyright notice and this permission notice shall be included in\r\n all copies or substantial portions of the Software.\r\n\r\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\n THE SOFTWARE.\r\n*/\r\n\r\nimport {\r\n    assert, cclegacy, clamp, geometry, gfx, Layers, Material, pipeline,\r\n    PipelineEventProcessor, PipelineEventType, ReflectionProbeManager, renderer,\r\n    rendering, sys, Vec2, Vec3, Vec4, warn,\r\n} from 'cc';\r\n\r\nimport { DEBUG, EDITOR } from 'cc/env';\r\n\r\nimport {\r\n    BloomType,\r\n    makePipelineSettings,\r\n    PipelineSettings,\r\n} from './builtin-pipeline-types';\r\n\r\nconst { AABB, Sphere, intersect } = geometry;\r\nconst { ClearFlagBit, Color, Format, FormatFeatureBit, LoadOp, StoreOp, TextureType, Viewport } = gfx;\r\nconst { scene } = renderer;\r\nconst { CameraUsage, CSMLevel, LightType } = scene;\r\n\r\nfunction forwardNeedClearColor(camera: renderer.scene.Camera): boolean {\r\n    return !!(camera.clearFlag & (ClearFlagBit.COLOR | (ClearFlagBit.STENCIL << 1)));\r\n}\r\n\r\nfunction getCsmMainLightViewport(\r\n    light: renderer.scene.DirectionalLight,\r\n    w: number,\r\n    h: number,\r\n    level: number,\r\n    vp: gfx.Viewport,\r\n    screenSpaceSignY: number,\r\n): void {\r\n    if (light.shadowFixedArea || light.csmLevel === CSMLevel.LEVEL_1) {\r\n        vp.left = 0;\r\n        vp.top = 0;\r\n        vp.width = Math.trunc(w);\r\n        vp.height = Math.trunc(h);\r\n    } else {\r\n        vp.left = Math.trunc(level % 2 * 0.5 * w);\r\n        if (screenSpaceSignY > 0) {\r\n            vp.top = Math.trunc((1 - Math.floor(level / 2)) * 0.5 * h);\r\n        } else {\r\n            vp.top = Math.trunc(Math.floor(level / 2) * 0.5 * h);\r\n        }\r\n        vp.width = Math.trunc(0.5 * w);\r\n        vp.height = Math.trunc(0.5 * h);\r\n    }\r\n    vp.left = Math.max(0, vp.left);\r\n    vp.top = Math.max(0, vp.top);\r\n    vp.width = Math.max(1, vp.width);\r\n    vp.height = Math.max(1, vp.height);\r\n}\r\n\r\nexport class PipelineConfigs {\r\n    isWeb = false;\r\n    isWebGL1 = false;\r\n    isWebGPU = false;\r\n    isMobile = false;\r\n    isHDR = false;\r\n    useFloatOutput = false;\r\n    toneMappingType = 0; // 0: ACES, 1: None\r\n    shadowEnabled = false;\r\n    shadowMapFormat = Format.R32F;\r\n    shadowMapSize = new Vec2(1, 1);\r\n    usePlanarShadow = false;\r\n    screenSpaceSignY = 1;\r\n    supportDepthSample = false;\r\n    mobileMaxSpotLightShadowMaps = 1;\r\n\r\n    platform = new Vec4(0, 0, 0, 0);\r\n}\r\n\r\nfunction setupPipelineConfigs(\r\n    ppl: rendering.BasicPipeline,\r\n    configs: PipelineConfigs,\r\n): void {\r\n    const sampleFeature = FormatFeatureBit.SAMPLED_TEXTURE | FormatFeatureBit.LINEAR_FILTER;\r\n    const device = ppl.device;\r\n    // Platform\r\n    configs.isWeb = !sys.isNative;\r\n    configs.isWebGL1 = device.gfxAPI === gfx.API.WEBGL;\r\n    configs.isWebGPU = device.gfxAPI === gfx.API.WEBGPU;\r\n    configs.isMobile = sys.isMobile;\r\n\r\n    // Rendering\r\n    configs.isHDR = ppl.pipelineSceneData.isHDR; // Has tone mapping\r\n    configs.useFloatOutput = ppl.getMacroBool('CC_USE_FLOAT_OUTPUT');\r\n    configs.toneMappingType = ppl.pipelineSceneData.postSettings.toneMappingType;\r\n    // Shadow\r\n    const shadowInfo = ppl.pipelineSceneData.shadows;\r\n    configs.shadowEnabled = shadowInfo.enabled;\r\n    configs.shadowMapFormat = pipeline.supportsR32FloatTexture(ppl.device) ? Format.R32F : Format.RGBA8;\r\n    configs.shadowMapSize.set(shadowInfo.size);\r\n    configs.usePlanarShadow = shadowInfo.enabled && shadowInfo.type === renderer.scene.ShadowType.Planar;\r\n    // Device\r\n    configs.screenSpaceSignY = ppl.device.capabilities.screenSpaceSignY;\r\n    configs.supportDepthSample = (ppl.device.getFormatFeatures(Format.DEPTH_STENCIL) & sampleFeature) === sampleFeature;\r\n    // Constants\r\n    const screenSpaceSignY = device.capabilities.screenSpaceSignY;\r\n    configs.platform.x = configs.isMobile ? 1.0 : 0.0;\r\n    configs.platform.w = (screenSpaceSignY * 0.5 + 0.5) << 1 | (device.capabilities.clipSpaceSignY * 0.5 + 0.5);\r\n}\r\n\r\nexport interface PipelineSettings2 extends PipelineSettings {\r\n    _passes?: rendering.PipelinePassBuilder[];\r\n}\r\n\r\nconst defaultSettings = makePipelineSettings();\r\n\r\nexport class CameraConfigs {\r\n    settings: PipelineSettings = defaultSettings;\r\n    // Window\r\n    isMainGameWindow = false;\r\n    renderWindowId = 0;\r\n    // Camera\r\n    colorName = '';\r\n    depthStencilName = '';\r\n    // Pipeline\r\n    enableFullPipeline = false;\r\n    enableProfiler = false;\r\n    remainingPasses = 0;\r\n    // Shading Scale\r\n    enableShadingScale = false;\r\n    shadingScale = 1.0;\r\n    nativeWidth = 1;\r\n    nativeHeight = 1;\r\n    width = 1; // Scaled width\r\n    height = 1; // Scaled height\r\n    // Radiance\r\n    enableHDR = false;\r\n    radianceFormat = gfx.Format.RGBA8;\r\n    // Tone Mapping\r\n    copyAndTonemapMaterial: Material | null = null;\r\n    // Depth\r\n    /** @en mutable */\r\n    enableStoreSceneDepth = false;\r\n}\r\n\r\nconst sClearColorTransparentBlack = new Color(0, 0, 0, 0);\r\n\r\nfunction sortPipelinePassBuildersByConfigOrder(passBuilders: rendering.PipelinePassBuilder[]): void {\r\n    passBuilders.sort((a, b) => {\r\n        return a.getConfigOrder() - b.getConfigOrder();\r\n    });\r\n}\r\n\r\nfunction sortPipelinePassBuildersByRenderOrder(passBuilders: rendering.PipelinePassBuilder[]): void {\r\n    passBuilders.sort((a, b) => {\r\n        return a.getRenderOrder() - b.getRenderOrder();\r\n    });\r\n}\r\n\r\nfunction addCopyToScreenPass(\r\n    ppl: rendering.BasicPipeline,\r\n    pplConfigs: Readonly<PipelineConfigs>,\r\n    cameraConfigs: CameraConfigs,\r\n    input: string,\r\n): rendering.BasicRenderPassBuilder {\r\n    assert(!!cameraConfigs.copyAndTonemapMaterial);\r\n    const pass = ppl.addRenderPass(\r\n        cameraConfigs.nativeWidth,\r\n        cameraConfigs.nativeHeight,\r\n        'cc-tone-mapping');\r\n    pass.addRenderTarget(\r\n        cameraConfigs.colorName,\r\n        LoadOp.CLEAR, StoreOp.STORE,\r\n        sClearColorTransparentBlack);\r\n    pass.addTexture(input, 'inputTexture');\r\n    pass.addQueue(rendering.QueueHint.OPAQUE)\r\n        .addFullscreenQuad(cameraConfigs.copyAndTonemapMaterial, 1);\r\n    return pass;\r\n}\r\n\r\nexport function getPingPongRenderTarget(prevName: string, prefix: string, id: number): string {\r\n    if (prevName.startsWith(prefix)) {\r\n        return `${prefix}${1 - Number(prevName.charAt(prefix.length))}_${id}`;\r\n    } else {\r\n        return `${prefix}0_${id}`;\r\n    }\r\n}\r\n\r\nexport interface PipelineContext {\r\n    colorName: string;\r\n    depthStencilName: string;\r\n}\r\n\r\nclass ForwardLighting {\r\n    // Active lights\r\n    private readonly lights: renderer.scene.Light[] = [];\r\n    // Active spot lights with shadows (Mutually exclusive with `lights`)\r\n    private readonly shadowEnabledSpotLights: renderer.scene.SpotLight[] = [];\r\n\r\n    // Internal cached resources\r\n    private readonly _sphere = Sphere.create(0, 0, 0, 1);\r\n    private readonly _boundingBox = new AABB();\r\n    private readonly _rangedDirLightBoundingBox = new AABB(0.0, 0.0, 0.0, 0.5, 0.5, 0.5);\r\n\r\n    // ----------------------------------------------------------------\r\n    // Interface\r\n    // ----------------------------------------------------------------\r\n    public cullLights(scene: renderer.RenderScene, frustum: geometry.Frustum, cameraPos?: Vec3): void {\r\n        // TODO(zhouzhenglong): Make light culling native\r\n        this.lights.length = 0;\r\n        this.shadowEnabledSpotLights.length = 0;\r\n        // spot lights\r\n        for (const light of scene.spotLights) {\r\n            if (light.baked) {\r\n                continue;\r\n            }\r\n            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);\r\n            if (intersect.sphereFrustum(this._sphere, frustum)) {\r\n                if (light.shadowEnabled) {\r\n                    this.shadowEnabledSpotLights.push(light);\r\n                } else {\r\n                    this.lights.push(light);\r\n                }\r\n            }\r\n        }\r\n        // sphere lights\r\n        for (const light of scene.sphereLights) {\r\n            if (light.baked) {\r\n                continue;\r\n            }\r\n            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);\r\n            if (intersect.sphereFrustum(this._sphere, frustum)) {\r\n                this.lights.push(light);\r\n            }\r\n        }\r\n        // point lights\r\n        for (const light of scene.pointLights) {\r\n            if (light.baked) {\r\n                continue;\r\n            }\r\n            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);\r\n            if (intersect.sphereFrustum(this._sphere, frustum)) {\r\n                this.lights.push(light);\r\n            }\r\n        }\r\n        // ranged dir lights\r\n        for (const light of scene.rangedDirLights) {\r\n            AABB.transform(this._boundingBox, this._rangedDirLightBoundingBox, light.node!.getWorldMatrix());\r\n            if (intersect.aabbFrustum(this._boundingBox, frustum)) {\r\n                this.lights.push(light);\r\n            }\r\n        }\r\n\r\n        if (cameraPos) {\r\n            this.shadowEnabledSpotLights.sort(\r\n                (lhs, rhs) => Vec3.squaredDistance(cameraPos, lhs.position) - Vec3.squaredDistance(cameraPos, rhs.position),\r\n            );\r\n        }\r\n    }\r\n    private _addLightQueues(camera: renderer.scene.Camera, pass: rendering.BasicRenderPassBuilder): void {\r\n        for (const light of this.lights) {\r\n            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');\r\n            switch (light.type) {\r\n                case LightType.SPHERE:\r\n                    queue.name = 'sphere-light';\r\n                    break;\r\n                case LightType.SPOT:\r\n                    queue.name = 'spot-light';\r\n                    break;\r\n                case LightType.POINT:\r\n                    queue.name = 'point-light';\r\n                    break;\r\n                case LightType.RANGED_DIRECTIONAL:\r\n                    queue.name = 'ranged-directional-light';\r\n                    break;\r\n                default:\r\n                    queue.name = 'unknown-light';\r\n            }\r\n            queue.addScene(\r\n                camera,\r\n                rendering.SceneFlags.BLEND,\r\n                light,\r\n            );\r\n        }\r\n    }\r\n    public addSpotlightShadowPasses(\r\n        ppl: rendering.BasicPipeline,\r\n        camera: renderer.scene.Camera,\r\n        maxNumShadowMaps: number,\r\n    ): void {\r\n        let i = 0;\r\n        for (const light of this.shadowEnabledSpotLights) {\r\n            const shadowMapSize = ppl.pipelineSceneData.shadows.size;\r\n            const shadowPass = ppl.addRenderPass(shadowMapSize.x, shadowMapSize.y, 'default');\r\n            shadowPass.name = `SpotLightShadowPass${i}`;\r\n            shadowPass.addRenderTarget(`SpotShadowMap${i}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));\r\n            shadowPass.addDepthStencil(`SpotShadowDepth${i}`, LoadOp.CLEAR, StoreOp.DISCARD);\r\n            shadowPass.addQueue(rendering.QueueHint.NONE, 'shadow-caster')\r\n                .addScene(camera, rendering.SceneFlags.OPAQUE | rendering.SceneFlags.MASK | rendering.SceneFlags.SHADOW_CASTER)\r\n                .useLightFrustum(light);\r\n            ++i;\r\n            if (i >= maxNumShadowMaps) {\r\n                break;\r\n            }\r\n        }\r\n    }\r\n    public addLightQueues(pass: rendering.BasicRenderPassBuilder,\r\n        camera: renderer.scene.Camera, maxNumShadowMaps: number): void {\r\n        this._addLightQueues(camera, pass);\r\n        let i = 0;\r\n        for (const light of this.shadowEnabledSpotLights) {\r\n            // Add spot-light pass\r\n            // Save last RenderPass to the `pass` variable\r\n            // TODO(zhouzhenglong): Fix per queue addTexture\r\n            pass.addTexture(`SpotShadowMap${i}`, 'cc_spotShadowMap');\r\n            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');\r\n            queue.addScene(camera, rendering.SceneFlags.BLEND, light);\r\n            ++i;\r\n            if (i >= maxNumShadowMaps) {\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    // Notice: ForwardLighting cannot handle a lot of lights.\r\n    // If there are too many lights, the performance will be very poor.\r\n    // If many lights are needed, please implement a forward+ or deferred rendering pipeline.\r\n    public addLightPasses(\r\n        colorName: string,\r\n        depthStencilName: string,\r\n        depthStencilStoreOp: gfx.StoreOp,\r\n        id: number, // window id\r\n        width: number,\r\n        height: number,\r\n        camera: renderer.scene.Camera,\r\n        viewport: gfx.Viewport,\r\n        ppl: rendering.BasicPipeline,\r\n        pass: rendering.BasicRenderPassBuilder,\r\n    ): rendering.BasicRenderPassBuilder {\r\n        this._addLightQueues(camera, pass);\r\n\r\n        let count = 0;\r\n        const shadowMapSize = ppl.pipelineSceneData.shadows.size;\r\n        for (const light of this.shadowEnabledSpotLights) {\r\n            const shadowPass = ppl.addRenderPass(shadowMapSize.x, shadowMapSize.y, 'default');\r\n            shadowPass.name = 'SpotlightShadowPass';\r\n            // Reuse csm shadow map\r\n            shadowPass.addRenderTarget(`ShadowMap${id}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));\r\n            shadowPass.addDepthStencil(`ShadowDepth${id}`, LoadOp.CLEAR, StoreOp.DISCARD);\r\n            shadowPass.addQueue(rendering.QueueHint.NONE, 'shadow-caster')\r\n                .addScene(camera, rendering.SceneFlags.OPAQUE | rendering.SceneFlags.MASK | rendering.SceneFlags.SHADOW_CASTER)\r\n                .useLightFrustum(light);\r\n\r\n            // Add spot-light pass\r\n            // Save last RenderPass to the `pass` variable\r\n            ++count;\r\n            const storeOp = count === this.shadowEnabledSpotLights.length\r\n                ? depthStencilStoreOp\r\n                : StoreOp.STORE;\r\n\r\n            pass = ppl.addRenderPass(width, height, 'default');\r\n            pass.name = 'SpotlightWithShadowMap';\r\n            pass.setViewport(viewport);\r\n            pass.addRenderTarget(colorName, LoadOp.LOAD);\r\n            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, storeOp);\r\n            pass.addTexture(`ShadowMap${id}`, 'cc_spotShadowMap');\r\n            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');\r\n            queue.addScene(\r\n                camera,\r\n                rendering.SceneFlags.BLEND,\r\n                light,\r\n            );\r\n        }\r\n        return pass;\r\n    }\r\n\r\n    public isMultipleLightPassesNeeded(): boolean {\r\n        return this.shadowEnabledSpotLights.length > 0;\r\n    }\r\n}\r\n\r\nexport interface ForwardPassConfigs {\r\n    enableMainLightShadowMap: boolean;\r\n    enableMainLightPlanarShadowMap: boolean;\r\n    enablePlanarReflectionProbe: boolean;\r\n    enableMSAA: boolean;\r\n    enableSingleForwardPass: boolean;\r\n}\r\n\r\nexport class BuiltinForwardPassBuilder implements rendering.PipelinePassBuilder {\r\n    static ConfigOrder = 100;\r\n    static RenderOrder = 100;\r\n    getConfigOrder(): number {\r\n        return BuiltinForwardPassBuilder.ConfigOrder;\r\n    }\r\n    getRenderOrder(): number {\r\n        return BuiltinForwardPassBuilder.RenderOrder;\r\n    }\r\n    configCamera(\r\n        camera: Readonly<renderer.scene.Camera>,\r\n        pipelineConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & ForwardPassConfigs): void {\r\n        // Shadow\r\n        cameraConfigs.enableMainLightShadowMap = pipelineConfigs.shadowEnabled\r\n            && !pipelineConfigs.usePlanarShadow\r\n            && !!camera.scene\r\n            && !!camera.scene.mainLight\r\n            && camera.scene.mainLight.shadowEnabled;\r\n\r\n        cameraConfigs.enableMainLightPlanarShadowMap = pipelineConfigs.shadowEnabled\r\n            && pipelineConfigs.usePlanarShadow\r\n            && !!camera.scene\r\n            && !!camera.scene.mainLight\r\n            && camera.scene.mainLight.shadowEnabled;\r\n\r\n        // Reflection Probe\r\n        cameraConfigs.enablePlanarReflectionProbe = cameraConfigs.isMainGameWindow\r\n            || camera.cameraUsage === CameraUsage.SCENE_VIEW\r\n            || camera.cameraUsage === CameraUsage.GAME_VIEW;\r\n\r\n        // MSAA\r\n        cameraConfigs.enableMSAA = cameraConfigs.settings.msaa.enabled\r\n            && !cameraConfigs.enableStoreSceneDepth // Cannot store MS depth, resolve depth is also not cross-platform\r\n            && !pipelineConfigs.isWeb // TODO(zhouzhenglong): remove this constraint\r\n            && !pipelineConfigs.isWebGL1;\r\n\r\n        // Forward rendering (Depend on MSAA and TBR)\r\n        cameraConfigs.enableSingleForwardPass\r\n            = pipelineConfigs.isMobile || cameraConfigs.enableMSAA;\r\n\r\n        ++cameraConfigs.remainingPasses;\r\n    }\r\n    windowResize(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\r\n        window: renderer.RenderWindow,\r\n        camera: renderer.scene.Camera,\r\n        nativeWidth: number,\r\n        nativeHeight: number): void {\r\n        const ResourceFlags = rendering.ResourceFlags;\r\n        const ResourceResidency = rendering.ResourceResidency;\r\n        const id = window.renderWindowId;\r\n        const settings = cameraConfigs.settings;\r\n\r\n        const width = cameraConfigs.enableShadingScale\r\n            ? Math.max(Math.floor(nativeWidth * cameraConfigs.shadingScale), 1)\r\n            : nativeWidth;\r\n        const height = cameraConfigs.enableShadingScale\r\n            ? Math.max(Math.floor(nativeHeight * cameraConfigs.shadingScale), 1)\r\n            : nativeHeight;\r\n\r\n        // MsaaRadiance\r\n        if (cameraConfigs.enableMSAA) {\r\n            // Notice: We never store multisample results.\r\n            // These samples are always resolved and discarded at the end of the render pass.\r\n            // So the ResourceResidency should be MEMORYLESS.\r\n            if (cameraConfigs.enableHDR) {\r\n                ppl.addTexture(`MsaaRadiance${id}`, TextureType.TEX2D, cameraConfigs.radianceFormat, width, height, 1, 1, 1,\r\n                    settings.msaa.sampleCount, ResourceFlags.COLOR_ATTACHMENT, ResourceResidency.MEMORYLESS);\r\n            } else {\r\n                ppl.addTexture(`MsaaRadiance${id}`, TextureType.TEX2D, Format.RGBA8, width, height, 1, 1, 1,\r\n                    settings.msaa.sampleCount, ResourceFlags.COLOR_ATTACHMENT, ResourceResidency.MEMORYLESS);\r\n            }\r\n            ppl.addTexture(`MsaaDepthStencil${id}`, TextureType.TEX2D, Format.DEPTH_STENCIL, width, height, 1, 1, 1,\r\n                settings.msaa.sampleCount, ResourceFlags.DEPTH_STENCIL_ATTACHMENT, ResourceResidency.MEMORYLESS);\r\n        }\r\n\r\n        // Mainlight ShadowMap\r\n        ppl.addRenderTarget(\r\n            `ShadowMap${id}`,\r\n            pplConfigs.shadowMapFormat,\r\n            pplConfigs.shadowMapSize.x,\r\n            pplConfigs.shadowMapSize.y,\r\n        );\r\n        ppl.addDepthStencil(\r\n            `ShadowDepth${id}`,\r\n            Format.DEPTH_STENCIL,\r\n            pplConfigs.shadowMapSize.x,\r\n            pplConfigs.shadowMapSize.y,\r\n        );\r\n\r\n        // Spot-light shadow maps\r\n        if (cameraConfigs.enableSingleForwardPass) {\r\n            const count = pplConfigs.mobileMaxSpotLightShadowMaps;\r\n            for (let i = 0; i !== count; ++i) {\r\n                ppl.addRenderTarget(\r\n                    `SpotShadowMap${i}`,\r\n                    pplConfigs.shadowMapFormat,\r\n                    pplConfigs.shadowMapSize.x,\r\n                    pplConfigs.shadowMapSize.y,\r\n                );\r\n                ppl.addDepthStencil(\r\n                    `SpotShadowDepth${i}`,\r\n                    Format.DEPTH_STENCIL,\r\n                    pplConfigs.shadowMapSize.x,\r\n                    pplConfigs.shadowMapSize.y,\r\n                );\r\n            }\r\n        }\r\n    }\r\n    setup(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & ForwardPassConfigs,\r\n        camera: renderer.scene.Camera,\r\n        context: PipelineContext): rendering.BasicRenderPassBuilder | undefined {\r\n        // Add global constants\r\n        ppl.setVec4('g_platform', pplConfigs.platform);\r\n\r\n        const id = camera.window.renderWindowId;\r\n\r\n        const scene = camera.scene!;\r\n        const mainLight = scene.mainLight;\r\n\r\n        --cameraConfigs.remainingPasses;\r\n        assert(cameraConfigs.remainingPasses >= 0);\r\n\r\n        // Forward Lighting (Light Culling)\r\n        this.forwardLighting.cullLights(scene, camera.frustum);\r\n\r\n        // Main Directional light CSM Shadow Map\r\n        if (cameraConfigs.enableMainLightShadowMap) {\r\n            assert(!!mainLight);\r\n            this._addCascadedShadowMapPass(ppl, pplConfigs, id, mainLight, camera);\r\n        }\r\n\r\n        // Spot light shadow maps (Mobile or MSAA)\r\n        if (cameraConfigs.enableSingleForwardPass) {\r\n            // Currently, only support 1 spot light with shadow map on mobile platform.\r\n            // TODO(zhouzhenglong): Relex this limitation.\r\n            this.forwardLighting.addSpotlightShadowPasses(\r\n                ppl, camera, pplConfigs.mobileMaxSpotLightShadowMaps);\r\n        }\r\n\r\n        this._tryAddReflectionProbePasses(ppl, cameraConfigs, id, mainLight, camera.scene);\r\n\r\n        if (cameraConfigs.remainingPasses > 0 || cameraConfigs.enableShadingScale) {\r\n            context.colorName = cameraConfigs.enableShadingScale\r\n                ? `ScaledRadiance0_${id}`\r\n                : `Radiance0_${id}`;\r\n            context.depthStencilName = cameraConfigs.enableShadingScale\r\n                ? `ScaledSceneDepth_${id}`\r\n                : `SceneDepth_${id}`;\r\n        } else {\r\n            context.colorName = cameraConfigs.colorName;\r\n            context.depthStencilName = cameraConfigs.depthStencilName;\r\n        }\r\n\r\n        const pass = this._addForwardRadiancePasses(\r\n            ppl, pplConfigs, cameraConfigs, id, camera,\r\n            cameraConfigs.width, cameraConfigs.height, mainLight,\r\n            context.colorName, context.depthStencilName,\r\n            !cameraConfigs.enableMSAA,\r\n            cameraConfigs.enableStoreSceneDepth ? StoreOp.STORE : StoreOp.DISCARD);\r\n\r\n        if (!cameraConfigs.enableStoreSceneDepth) {\r\n            context.depthStencilName = '';\r\n        }\r\n\r\n        if (cameraConfigs.remainingPasses === 0 && cameraConfigs.enableShadingScale) {\r\n            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, context.colorName);\r\n        } else {\r\n            return pass;\r\n        }\r\n    }\r\n    private _addCascadedShadowMapPass(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        id: number,\r\n        light: renderer.scene.DirectionalLight,\r\n        camera: renderer.scene.Camera,\r\n    ): void {\r\n        const QueueHint = rendering.QueueHint;\r\n        const SceneFlags = rendering.SceneFlags;\r\n        // ----------------------------------------------------------------\r\n        // Dynamic states\r\n        // ----------------------------------------------------------------\r\n        const shadowSize = ppl.pipelineSceneData.shadows.size;\r\n        const width = shadowSize.x;\r\n        const height = shadowSize.y;\r\n\r\n        const viewport = this._viewport;\r\n        viewport.left = viewport.top = 0;\r\n        viewport.width = width;\r\n        viewport.height = height;\r\n\r\n        // ----------------------------------------------------------------\r\n        // CSM Shadow Map\r\n        // ----------------------------------------------------------------\r\n        const pass = ppl.addRenderPass(width, height, 'default');\r\n        pass.name = 'CascadedShadowMap';\r\n        pass.addRenderTarget(`ShadowMap${id}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));\r\n        pass.addDepthStencil(`ShadowDepth${id}`, LoadOp.CLEAR, StoreOp.DISCARD);\r\n        const csmLevel = ppl.pipelineSceneData.csmSupported ? light.csmLevel : 1;\r\n\r\n        // Add shadow map viewports\r\n        for (let level = 0; level !== csmLevel; ++level) {\r\n            getCsmMainLightViewport(light, width, height, level, this._viewport, pplConfigs.screenSpaceSignY);\r\n            const queue = pass.addQueue(QueueHint.NONE, 'shadow-caster');\r\n            if (!pplConfigs.isWebGPU) { // Temporary workaround for WebGPU\r\n                queue.setViewport(this._viewport);\r\n            }\r\n            queue\r\n                .addScene(camera, SceneFlags.OPAQUE | SceneFlags.MASK | SceneFlags.SHADOW_CASTER)\r\n                .useLightFrustum(light, level);\r\n        }\r\n    }\r\n    private _tryAddReflectionProbePasses(\r\n        ppl: rendering.BasicPipeline,\r\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\r\n        id: number,\r\n        mainLight: renderer.scene.DirectionalLight | null,\r\n        scene: renderer.RenderScene | null,\r\n    ): void {\r\n        const reflectionProbeManager = cclegacy.internal.reflectionProbeManager as ReflectionProbeManager | undefined;\r\n        if (!reflectionProbeManager) {\r\n            return;\r\n        }\r\n        const ResourceResidency = rendering.ResourceResidency;\r\n        const probes = reflectionProbeManager.getProbes();\r\n        const maxProbeCount = 4;\r\n        let probeID = 0;\r\n        for (const probe of probes) {\r\n            if (!probe.needRender) {\r\n                continue;\r\n            }\r\n            const area = probe.renderArea();\r\n            const width = Math.max(Math.floor(area.x), 1);\r\n            const height = Math.max(Math.floor(area.y), 1);\r\n\r\n            if (probe.probeType === renderer.scene.ProbeType.PLANAR) {\r\n                if (!cameraConfigs.enablePlanarReflectionProbe) {\r\n                    continue;\r\n                }\r\n                const window: renderer.RenderWindow = probe.realtimePlanarTexture!.window!;\r\n                const colorName = `PlanarProbeRT${probeID}`;\r\n                const depthStencilName = `PlanarProbeDS${probeID}`;\r\n                // ProbeResource\r\n                ppl.addRenderWindow(colorName,\r\n                    cameraConfigs.radianceFormat, width, height, window);\r\n                ppl.addDepthStencil(depthStencilName,\r\n                    gfx.Format.DEPTH_STENCIL, width, height, ResourceResidency.MEMORYLESS);\r\n\r\n                // Rendering\r\n                const probePass = ppl.addRenderPass(width, height, 'default');\r\n                probePass.name = `PlanarReflectionProbe${probeID}`;\r\n                this._buildReflectionProbePass(probePass, cameraConfigs, id, probe.camera,\r\n                    colorName, depthStencilName, mainLight, scene);\r\n            } else if (EDITOR) {\r\n                for (let faceIdx = 0; faceIdx < probe.bakedCubeTextures.length; faceIdx++) {\r\n                    probe.updateCameraDir(faceIdx);\r\n                    const window: renderer.RenderWindow = probe.bakedCubeTextures[faceIdx].window!;\r\n                    const colorName = `CubeProbeRT${probeID}${faceIdx}`;\r\n                    const depthStencilName = `CubeProbeDS${probeID}${faceIdx}`;\r\n                    // ProbeResource\r\n                    ppl.addRenderWindow(colorName,\r\n                        cameraConfigs.radianceFormat, width, height, window);\r\n                    ppl.addDepthStencil(depthStencilName,\r\n                        gfx.Format.DEPTH_STENCIL, width, height, ResourceResidency.MEMORYLESS);\r\n\r\n                    // Rendering\r\n                    const probePass = ppl.addRenderPass(width, height, 'default');\r\n                    probePass.name = `CubeProbe${probeID}${faceIdx}`;\r\n                    this._buildReflectionProbePass(probePass, cameraConfigs, id, probe.camera,\r\n                        colorName, depthStencilName, mainLight, scene);\r\n                }\r\n                probe.needRender = false;\r\n            }\r\n            ++probeID;\r\n            if (probeID === maxProbeCount) {\r\n                break;\r\n            }\r\n        }\r\n    }\r\n    private _buildReflectionProbePass(\r\n        pass: rendering.BasicRenderPassBuilder,\r\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\r\n        id: number,\r\n        camera: renderer.scene.Camera,\r\n        colorName: string,\r\n        depthStencilName: string,\r\n        mainLight: renderer.scene.DirectionalLight | null,\r\n        scene: renderer.RenderScene | null = null,\r\n    ): void {\r\n        const QueueHint = rendering.QueueHint;\r\n        const SceneFlags = rendering.SceneFlags;\r\n        // set viewport\r\n        const colorStoreOp = cameraConfigs.enableMSAA ? StoreOp.DISCARD : StoreOp.STORE;\r\n\r\n        // bind output render target\r\n        if (forwardNeedClearColor(camera)) {\r\n            this._reflectionProbeClearColor.x = camera.clearColor.x;\r\n            this._reflectionProbeClearColor.y = camera.clearColor.y;\r\n            this._reflectionProbeClearColor.z = camera.clearColor.z;\r\n            const clearColor = rendering.packRGBE(this._reflectionProbeClearColor);\r\n            this._clearColor.x = clearColor.x;\r\n            this._clearColor.y = clearColor.y;\r\n            this._clearColor.z = clearColor.z;\r\n            this._clearColor.w = clearColor.w;\r\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, colorStoreOp, this._clearColor);\r\n        } else {\r\n            pass.addRenderTarget(colorName, LoadOp.LOAD, colorStoreOp);\r\n        }\r\n\r\n        // bind depth stencil buffer\r\n        if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {\r\n            pass.addDepthStencil(\r\n                depthStencilName,\r\n                LoadOp.CLEAR,\r\n                StoreOp.DISCARD,\r\n                camera.clearDepth,\r\n                camera.clearStencil,\r\n                camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,\r\n            );\r\n        } else {\r\n            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, StoreOp.DISCARD);\r\n        }\r\n\r\n        // Set shadow map if enabled\r\n        if (cameraConfigs.enableMainLightShadowMap) {\r\n            pass.addTexture(`ShadowMap${id}`, 'cc_shadowMap');\r\n        }\r\n\r\n        // TODO(zhouzhenglong): Separate OPAQUE and MASK queue\r\n\r\n        // add opaque and mask queue\r\n        pass.addQueue(QueueHint.NONE, 'reflect-map') // Currently we put OPAQUE and MASK into one queue, so QueueHint is NONE\r\n            .addScene(camera,\r\n                SceneFlags.OPAQUE | SceneFlags.MASK | SceneFlags.REFLECTION_PROBE,\r\n                mainLight || undefined,\r\n                scene ? scene : undefined);\r\n    }\r\n    private _addForwardRadiancePasses(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\r\n        id: number,\r\n        camera: renderer.scene.Camera,\r\n        width: number,\r\n        height: number,\r\n        mainLight: renderer.scene.DirectionalLight | null,\r\n        colorName: string,\r\n        depthStencilName: string,\r\n        disableMSAA: boolean = false,\r\n        depthStencilStoreOp: gfx.StoreOp = StoreOp.DISCARD,\r\n    ): rendering.BasicRenderPassBuilder {\r\n        const QueueHint = rendering.QueueHint;\r\n        const SceneFlags = rendering.SceneFlags;\r\n        // ----------------------------------------------------------------\r\n        // Dynamic states\r\n        // ----------------------------------------------------------------\r\n        // Prepare camera clear color\r\n        const clearColor = camera.clearColor; // Reduce C++/TS interop\r\n        this._clearColor.x = clearColor.x;\r\n        this._clearColor.y = clearColor.y;\r\n        this._clearColor.z = clearColor.z;\r\n        this._clearColor.w = clearColor.w;\r\n\r\n        // Prepare camera viewport\r\n        const viewport = camera.viewport; // Reduce C++/TS interop\r\n        this._viewport.left = Math.round(viewport.x * width);\r\n        this._viewport.top = Math.round(viewport.y * height);\r\n        // Here we must use camera.viewport.width instead of camera.viewport.z, which\r\n        // is undefined on native platform. The same as camera.viewport.height.\r\n        this._viewport.width = Math.max(Math.round(viewport.width * width), 1);\r\n        this._viewport.height = Math.max(Math.round(viewport.height * height), 1);\r\n\r\n        // MSAA\r\n        const enableMSAA = !disableMSAA && cameraConfigs.enableMSAA;\r\n        assert(!enableMSAA || cameraConfigs.enableSingleForwardPass);\r\n\r\n        // ----------------------------------------------------------------\r\n        // Forward Lighting (Main Directional Light)\r\n        // ----------------------------------------------------------------\r\n        const pass = cameraConfigs.enableSingleForwardPass\r\n            ? this._addForwardSingleRadiancePass(ppl, pplConfigs, cameraConfigs,\r\n                id, camera, enableMSAA, width, height, mainLight,\r\n                colorName, depthStencilName, depthStencilStoreOp)\r\n            : this._addForwardMultipleRadiancePasses(ppl, cameraConfigs,\r\n                id, camera, width, height, mainLight,\r\n                colorName, depthStencilName, depthStencilStoreOp);\r\n\r\n        // Planar Shadow\r\n        if (cameraConfigs.enableMainLightPlanarShadowMap) {\r\n            this._addPlanarShadowQueue(camera, mainLight, pass);\r\n        }\r\n\r\n        // ----------------------------------------------------------------\r\n        // Forward Lighting (Blend)\r\n        // ----------------------------------------------------------------\r\n        // Add transparent queue\r\n\r\n        const sceneFlags = SceneFlags.BLEND |\r\n            (camera.geometryRenderer\r\n                ? SceneFlags.GEOMETRY\r\n                : SceneFlags.NONE);\r\n\r\n        pass\r\n            .addQueue(QueueHint.BLEND)\r\n            .addScene(camera, sceneFlags, mainLight || undefined);\r\n\r\n        return pass;\r\n    }\r\n    private _addForwardSingleRadiancePass(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\r\n        id: number,\r\n        camera: renderer.scene.Camera,\r\n        enableMSAA: boolean,\r\n        width: number,\r\n        height: number,\r\n        mainLight: renderer.scene.DirectionalLight | null,\r\n        colorName: string,\r\n        depthStencilName: string,\r\n        depthStencilStoreOp: gfx.StoreOp\r\n    ): rendering.BasicRenderPassBuilder {\r\n        assert(cameraConfigs.enableSingleForwardPass);\r\n        // ----------------------------------------------------------------\r\n        // Forward Lighting (Main Directional Light)\r\n        // ----------------------------------------------------------------\r\n        let pass: rendering.BasicRenderPassBuilder;\r\n        if (enableMSAA) {\r\n            const msaaRadianceName = `MsaaRadiance${id}`;\r\n            const msaaDepthStencilName = `MsaaDepthStencil${id}`;\r\n            const sampleCount = cameraConfigs.settings.msaa.sampleCount;\r\n\r\n            const msPass = ppl.addMultisampleRenderPass(width, height, sampleCount, 0, 'default');\r\n            msPass.name = 'MsaaForwardPass';\r\n\r\n            // MSAA always discards depth stencil\r\n            this._buildForwardMainLightPass(msPass, cameraConfigs, id, camera,\r\n                msaaRadianceName, msaaDepthStencilName, StoreOp.DISCARD, mainLight);\r\n\r\n            msPass.resolveRenderTarget(msaaRadianceName, colorName);\r\n\r\n            pass = msPass;\r\n        } else {\r\n            pass = ppl.addRenderPass(width, height, 'default');\r\n            pass.name = 'ForwardPass';\r\n\r\n            this._buildForwardMainLightPass(pass, cameraConfigs, id, camera,\r\n                colorName, depthStencilName, depthStencilStoreOp, mainLight);\r\n        }\r\n        assert(pass !== undefined);\r\n\r\n        // Forward Lighting (Additive Lights)\r\n        this.forwardLighting.addLightQueues(\r\n            pass,\r\n            camera,\r\n            pplConfigs.mobileMaxSpotLightShadowMaps,\r\n        );\r\n\r\n        return pass;\r\n    }\r\n    private _addForwardMultipleRadiancePasses(\r\n        ppl: rendering.BasicPipeline,\r\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\r\n        id: number,\r\n        camera: renderer.scene.Camera,\r\n        width: number,\r\n        height: number,\r\n        mainLight: renderer.scene.DirectionalLight | null,\r\n        colorName: string,\r\n        depthStencilName: string,\r\n        depthStencilStoreOp: gfx.StoreOp\r\n    ): rendering.BasicRenderPassBuilder {\r\n        assert(!cameraConfigs.enableSingleForwardPass);\r\n\r\n        // Forward Lighting (Main Directional Light)\r\n        let pass = ppl.addRenderPass(width, height, 'default');\r\n        pass.name = 'ForwardPass';\r\n\r\n        const firstStoreOp = this.forwardLighting.isMultipleLightPassesNeeded()\r\n            ? StoreOp.STORE\r\n            : depthStencilStoreOp;\r\n\r\n        this._buildForwardMainLightPass(pass, cameraConfigs,\r\n            id, camera, colorName, depthStencilName, firstStoreOp, mainLight);\r\n\r\n        // Forward Lighting (Additive Lights)\r\n        pass = this.forwardLighting\r\n            .addLightPasses(colorName, depthStencilName, depthStencilStoreOp,\r\n                id, width, height, camera, this._viewport, ppl, pass);\r\n\r\n        return pass;\r\n    }\r\n    private _buildForwardMainLightPass(\r\n        pass: rendering.BasicRenderPassBuilder,\r\n        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,\r\n        id: number,\r\n        camera: renderer.scene.Camera,\r\n        colorName: string,\r\n        depthStencilName: string,\r\n        depthStencilStoreOp: gfx.StoreOp,\r\n        mainLight: renderer.scene.DirectionalLight | null,\r\n        scene: renderer.RenderScene | null = null,\r\n    ): void {\r\n        const QueueHint = rendering.QueueHint;\r\n        const SceneFlags = rendering.SceneFlags;\r\n        // set viewport\r\n        pass.setViewport(this._viewport);\r\n\r\n        const colorStoreOp = cameraConfigs.enableMSAA ? StoreOp.DISCARD : StoreOp.STORE;\r\n\r\n        // bind output render target\r\n        if (forwardNeedClearColor(camera)) {\r\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, colorStoreOp, this._clearColor);\r\n        } else {\r\n            pass.addRenderTarget(colorName, LoadOp.LOAD, colorStoreOp);\r\n        }\r\n\r\n        // bind depth stencil buffer\r\n        if (DEBUG) {\r\n            if (colorName === cameraConfigs.colorName &&\r\n                depthStencilName !== cameraConfigs.depthStencilName) {\r\n                warn('Default framebuffer cannot use custom depth stencil buffer');\r\n            }\r\n        }\r\n\r\n        if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {\r\n            pass.addDepthStencil(\r\n                depthStencilName,\r\n                LoadOp.CLEAR,\r\n                depthStencilStoreOp,\r\n                camera.clearDepth,\r\n                camera.clearStencil,\r\n                camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,\r\n            );\r\n        } else {\r\n            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, depthStencilStoreOp);\r\n        }\r\n\r\n        // Set shadow map if enabled\r\n        if (cameraConfigs.enableMainLightShadowMap) {\r\n            pass.addTexture(`ShadowMap${id}`, 'cc_shadowMap');\r\n        }\r\n\r\n        // TODO(zhouzhenglong): Separate OPAQUE and MASK queue\r\n\r\n        // add opaque and mask queue\r\n        pass.addQueue(QueueHint.NONE) // Currently we put OPAQUE and MASK into one queue, so QueueHint is NONE\r\n            .addScene(camera,\r\n                SceneFlags.OPAQUE | SceneFlags.MASK,\r\n                mainLight || undefined,\r\n                scene ? scene : undefined);\r\n    }\r\n    private _addPlanarShadowQueue(\r\n        camera: renderer.scene.Camera,\r\n        mainLight: renderer.scene.DirectionalLight | null,\r\n        pass: rendering.BasicRenderPassBuilder,\r\n    ) {\r\n        const QueueHint = rendering.QueueHint;\r\n        const SceneFlags = rendering.SceneFlags;\r\n        pass.addQueue(QueueHint.BLEND, 'planar-shadow')\r\n            .addScene(\r\n                camera,\r\n                SceneFlags.SHADOW_CASTER | SceneFlags.PLANAR_SHADOW | SceneFlags.BLEND,\r\n                mainLight || undefined,\r\n            );\r\n    }\r\n    private readonly forwardLighting = new ForwardLighting();\r\n    private readonly _viewport = new Viewport();\r\n    private readonly _clearColor = new Color(0, 0, 0, 1);\r\n    private readonly _reflectionProbeClearColor = new Vec3(0, 0, 0);\r\n}\r\n\r\nexport interface BloomPassConfigs {\r\n    enableBloom: boolean;\r\n}\r\n\r\nfunction downSize(size: number, scale: number): number {\r\n    return Math.max(Math.floor(size * scale), 1);\r\n}\r\n\r\ninterface RenderTextureDesc {\r\n    name: string;\r\n    width: number;\r\n    height: number;\r\n}\r\n\r\nexport class BuiltinBloomPassBuilder implements rendering.PipelinePassBuilder {\r\n    getConfigOrder(): number {\r\n        return 0;\r\n    }\r\n    getRenderOrder(): number {\r\n        return 200;\r\n    }\r\n    configCamera(\r\n        camera: Readonly<renderer.scene.Camera>,\r\n        pipelineConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & BloomPassConfigs): void {\r\n        const { bloom } = cameraConfigs.settings;\r\n        const hasValidMaterial = (\r\n            bloom.type === BloomType.KawaseDualFilter && !!bloom.kawaseFilterMaterial ||\r\n            bloom.type === BloomType.MipmapFilter && !!bloom.mipmapFilterMaterial\r\n        );\r\n        cameraConfigs.enableBloom = bloom.enabled && hasValidMaterial;\r\n\r\n        if (cameraConfigs.enableBloom) {\r\n            ++cameraConfigs.remainingPasses;\r\n        }\r\n    }\r\n    windowResize(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & BloomPassConfigs,\r\n        window: renderer.RenderWindow): void {\r\n        if (!cameraConfigs.enableBloom) {\r\n            return;\r\n        }\r\n\r\n        const { width, height, settings: { bloom } } = cameraConfigs;\r\n        const id = window.renderWindowId;\r\n        const format = cameraConfigs.radianceFormat;\r\n\r\n        if (bloom.type === BloomType.KawaseDualFilter) {\r\n            let bloomWidth = cameraConfigs.width;\r\n            let bloomHeight = cameraConfigs.height;\r\n            for (let i = 0; i !== bloom.iterations + 1; ++i) {\r\n                bloomWidth = Math.max(Math.floor(bloomWidth / 2), 1);\r\n                bloomHeight = Math.max(Math.floor(bloomHeight / 2), 1);\r\n                ppl.addRenderTarget(`BloomTex${id}_${i}`, format, bloomWidth, bloomHeight);\r\n            }\r\n        } else if (bloom.type === BloomType.MipmapFilter) {\r\n            const iterations = bloom.iterations;\r\n            for (let i = 0; i !== iterations + 1; ++i) {\r\n                // DownSample\r\n                if (i < iterations) {\r\n                    const scale = Math.pow(0.5, i + 2);\r\n                    this._bloomDownSampleTexDescs[i] = this.createTexture(\r\n                        ppl,\r\n                        `DownSampleColor${id}${i}`,\r\n                        downSize(width, scale),\r\n                        downSize(height, scale),\r\n                        format);\r\n                }\r\n                // UpSample\r\n                if (i < iterations - 1) {\r\n                    const scale = Math.pow(0.5, iterations - i - 1);\r\n                    this._bloomUpSampleTexDescs[i] = this.createTexture(\r\n                        ppl,\r\n                        `UpSampleColor${id}${i}`,\r\n                        downSize(width, scale),\r\n                        downSize(height, scale),\r\n                        format);\r\n                }\r\n            }\r\n            this._originalColorDesc = this.createTexture(ppl, `OriginalColor${id}`, width, height, format);\r\n            this._prefilterTexDesc = this.createTexture(ppl, `PrefilterColor${id}`,\r\n                downSize(width, 0.5), downSize(height, 0.5), format);\r\n        }\r\n    }\r\n    private createTexture(\r\n        ppl: rendering.BasicPipeline,\r\n        name: string, width: number, height: number, format: number): RenderTextureDesc {\r\n        const desc = { name, width, height };\r\n        ppl.addRenderTarget(desc.name, format, desc.width, desc.height);\r\n        return desc;\r\n    }\r\n\r\n    setup(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & BloomPassConfigs,\r\n        camera: renderer.scene.Camera,\r\n        context: PipelineContext,\r\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\r\n        : rendering.BasicRenderPassBuilder | undefined {\r\n        if (!cameraConfigs.enableBloom) {\r\n            return prevRenderPass;\r\n        }\r\n\r\n        --cameraConfigs.remainingPasses;\r\n        assert(cameraConfigs.remainingPasses >= 0);\r\n\r\n        const bloom = cameraConfigs.settings.bloom;\r\n        const id = camera.window.renderWindowId;\r\n\r\n        switch (bloom.type) {\r\n            case BloomType.KawaseDualFilter: {\r\n                const material = bloom.kawaseFilterMaterial;\r\n                assert(!!material);\r\n                return this._addKawaseDualFilterBloomPasses(\r\n                    ppl, pplConfigs,\r\n                    cameraConfigs,\r\n                    cameraConfigs.settings,\r\n                    material,\r\n                    id,\r\n                    cameraConfigs.width,\r\n                    cameraConfigs.height,\r\n                    context.colorName);\r\n            }\r\n            case BloomType.MipmapFilter: {\r\n                const material = bloom.mipmapFilterMaterial;\r\n                assert(!!material);\r\n                return this._addMipmapFilterBloomPasses(\r\n                    ppl, pplConfigs,\r\n                    cameraConfigs,\r\n                    cameraConfigs.settings,\r\n                    material,\r\n                    id,\r\n                    cameraConfigs.width,\r\n                    cameraConfigs.height,\r\n                    context.colorName);\r\n            }\r\n            default:\r\n                return prevRenderPass;\r\n        }\r\n    }\r\n    private _addKawaseDualFilterBloomPasses(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & Readonly<BloomPassConfigs>,\r\n        settings: PipelineSettings,\r\n        bloomMaterial: Material,\r\n        id: number,\r\n        width: number,\r\n        height: number,\r\n        radianceName: string,\r\n    ): rendering.BasicRenderPassBuilder {\r\n        const QueueHint = rendering.QueueHint;\r\n        // Based on Kawase Dual Filter Blur. Saves bandwidth on mobile devices.\r\n        // eslint-disable-next-line max-len\r\n        // https://community.arm.com/cfs-file/__key/communityserver-blogs-components-weblogfiles/00-00-00-20-66/siggraph2015_2D00_mmg_2D00_marius_2D00_slides.pdf\r\n\r\n        // Size: [prefilter(1/2), downsample(1/4), downsample(1/8), downsample(1/16), ...]\r\n        const iterations = settings.bloom.iterations;\r\n        const sizeCount = iterations + 1;\r\n        this._bloomWidths.length = sizeCount;\r\n        this._bloomHeights.length = sizeCount;\r\n        this._bloomWidths[0] = Math.max(Math.floor(width / 2), 1);\r\n        this._bloomHeights[0] = Math.max(Math.floor(height / 2), 1);\r\n        for (let i = 1; i !== sizeCount; ++i) {\r\n            this._bloomWidths[i] = Math.max(Math.floor(this._bloomWidths[i - 1] / 2), 1);\r\n            this._bloomHeights[i] = Math.max(Math.floor(this._bloomHeights[i - 1] / 2), 1);\r\n        }\r\n\r\n        // Bloom texture names\r\n        this._bloomTexNames.length = sizeCount;\r\n        for (let i = 0; i !== sizeCount; ++i) {\r\n            this._bloomTexNames[i] = `BloomTex${id}_${i}`;\r\n        }\r\n\r\n        // Setup bloom parameters\r\n        this._bloomParams.x = pplConfigs.useFloatOutput ? 1 : 0;\r\n        this._bloomParams.y = 0; // unused\r\n        this._bloomParams.z = settings.bloom.threshold;\r\n        this._bloomParams.w = settings.bloom.enableAlphaMask ? 1 : 0;\r\n\r\n        // Prefilter pass\r\n        const prefilterPass = ppl.addRenderPass(this._bloomWidths[0], this._bloomHeights[0], 'cc-bloom-prefilter');\r\n        prefilterPass.addRenderTarget(\r\n            this._bloomTexNames[0],\r\n            LoadOp.CLEAR,\r\n            StoreOp.STORE,\r\n            this._clearColorTransparentBlack,\r\n        );\r\n        prefilterPass.addTexture(radianceName, 'inputTexture');\r\n        prefilterPass.setVec4('bloomParams', this._bloomParams);\r\n        prefilterPass\r\n            .addQueue(QueueHint.OPAQUE)\r\n            .addFullscreenQuad(bloomMaterial, 0);\r\n\r\n        // Downsample passes\r\n        for (let i = 1; i !== sizeCount; ++i) {\r\n            const downPass = ppl.addRenderPass(this._bloomWidths[i], this._bloomHeights[i], 'cc-bloom-downsample');\r\n            downPass.addRenderTarget(this._bloomTexNames[i], LoadOp.CLEAR, StoreOp.STORE, this._clearColorTransparentBlack);\r\n            downPass.addTexture(this._bloomTexNames[i - 1], 'bloomTexture');\r\n            this._bloomTexSize.x = this._bloomWidths[i - 1];\r\n            this._bloomTexSize.y = this._bloomHeights[i - 1];\r\n            downPass.setVec4('bloomTexSize', this._bloomTexSize);\r\n            downPass\r\n                .addQueue(QueueHint.OPAQUE)\r\n                .addFullscreenQuad(bloomMaterial, 1);\r\n        }\r\n\r\n        // Upsample passes\r\n        for (let i = iterations; i-- > 0;) {\r\n            const upPass = ppl.addRenderPass(this._bloomWidths[i], this._bloomHeights[i], 'cc-bloom-upsample');\r\n            upPass.addRenderTarget(this._bloomTexNames[i], LoadOp.CLEAR, StoreOp.STORE, this._clearColorTransparentBlack);\r\n            upPass.addTexture(this._bloomTexNames[i + 1], 'bloomTexture');\r\n            this._bloomTexSize.x = this._bloomWidths[i + 1];\r\n            this._bloomTexSize.y = this._bloomHeights[i + 1];\r\n            upPass.setVec4('bloomTexSize', this._bloomTexSize);\r\n            upPass\r\n                .addQueue(QueueHint.OPAQUE)\r\n                .addFullscreenQuad(bloomMaterial, 2);\r\n        }\r\n\r\n        // Combine pass\r\n        this._bloomParams.w = settings.bloom.intensity;\r\n        const combinePass = ppl.addRenderPass(width, height, 'cc-bloom-combine');\r\n        combinePass.addRenderTarget(radianceName, LoadOp.LOAD, StoreOp.STORE);\r\n        combinePass.addTexture(this._bloomTexNames[0], 'bloomTexture');\r\n        combinePass.setVec4('bloomParams', this._bloomParams);\r\n        combinePass\r\n            .addQueue(QueueHint.BLEND)\r\n            .addFullscreenQuad(bloomMaterial, 3);\r\n\r\n        if (cameraConfigs.remainingPasses === 0) {\r\n            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, radianceName);\r\n        } else {\r\n            return combinePass;\r\n        }\r\n    }\r\n    private _addPass(\r\n        ppl: rendering.BasicPipeline,\r\n        width: number,\r\n        height: number,\r\n        layout: string,\r\n        colorName: string,\r\n        material: Material,\r\n        passIndex: number,\r\n        loadOp: gfx.LoadOp = LoadOp.CLEAR,\r\n        clearColor: gfx.Color = sClearColorTransparentBlack,\r\n        queueHint: rendering.QueueHint = rendering.QueueHint.OPAQUE): rendering.BasicRenderPassBuilder {\r\n        const pass = ppl.addRenderPass(width, height, layout);\r\n        pass.addRenderTarget(colorName, loadOp, StoreOp.STORE, clearColor);\r\n        pass.addQueue(queueHint)\r\n            .addFullscreenQuad(material, passIndex);\r\n        return pass;\r\n    }\r\n    private _addMipmapFilterBloomPasses(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & Readonly<BloomPassConfigs>,\r\n        settings: PipelineSettings,\r\n        bloomMaterial: Material,\r\n        id: number,\r\n        width: number,\r\n        height: number,\r\n        radianceName: string,\r\n    ): rendering.BasicRenderPassBuilder {\r\n        // Setup bloom parameters\r\n        this._bloomParams.x = pplConfigs.useFloatOutput ? 1 : 0;\r\n        this._bloomParams.x = 0; // unused\r\n        this._bloomParams.z = settings.bloom.threshold;\r\n        this._bloomParams.w = settings.bloom.intensity;\r\n        const prefilterInfo = this._prefilterTexDesc;\r\n\r\n        // Prefilter pass\r\n        let currSamplePass = this._addPass(\r\n            ppl,\r\n            prefilterInfo.width,\r\n            prefilterInfo.height,\r\n            'cc-bloom-mipmap-prefilter',\r\n            prefilterInfo.name,\r\n            bloomMaterial,\r\n            0,\r\n        );\r\n        currSamplePass.addTexture(radianceName, 'mainTexture');\r\n        currSamplePass.setVec4('bloomParams', this._bloomParams);\r\n\r\n        const downSampleInfos = this._bloomDownSampleTexDescs;\r\n        // Downsample passes\r\n        for (let i = 0; i < downSampleInfos.length; ++i) {\r\n            const currInfo = downSampleInfos[i];\r\n            const samplerSrc = i === 0 ? prefilterInfo : downSampleInfos[i - 1];\r\n            const samplerSrcName = samplerSrc.name;\r\n            this._bloomTexSize.x = 1 / samplerSrc.width;\r\n            this._bloomTexSize.y = 1 / samplerSrc.height;\r\n            currSamplePass = this._addPass(\r\n                ppl,\r\n                currInfo.width,\r\n                currInfo.height,\r\n                'cc-bloom-mipmap-downsample',\r\n                currInfo.name,\r\n                bloomMaterial,\r\n                1,\r\n            );\r\n            currSamplePass.addTexture(samplerSrcName, 'mainTexture');\r\n            currSamplePass.setVec4('bloomParams', this._bloomTexSize);\r\n        }\r\n        const lastIndex = downSampleInfos.length - 1;\r\n        const upSampleInfos = this._bloomUpSampleTexDescs;\r\n        // Upsample passes\r\n        for (let i = 0; i < upSampleInfos.length; i++) {\r\n            const currInfo = upSampleInfos[i];\r\n            const sampleSrc = i === 0 ? downSampleInfos[lastIndex] : upSampleInfos[i - 1];\r\n            const sampleSrcName = sampleSrc.name;\r\n            this._bloomTexSize.x = 1 / sampleSrc.width;\r\n            this._bloomTexSize.y = 1 / sampleSrc.height;\r\n            currSamplePass = this._addPass(\r\n                ppl,\r\n                currInfo.width,\r\n                currInfo.height,\r\n                'cc-bloom-mipmap-upsample',\r\n                currInfo.name,\r\n                bloomMaterial,\r\n                2,\r\n            );\r\n            currSamplePass.addTexture(sampleSrcName, 'mainTexture');\r\n            currSamplePass.addTexture(downSampleInfos[lastIndex - 1 - i].name, 'downsampleTexture');\r\n            currSamplePass.setVec4('bloomParams', this._bloomTexSize);\r\n        }\r\n\r\n        // Combine pass\r\n        const combinePass = this._addPass(\r\n            ppl,\r\n            width,\r\n            height,\r\n            'cc-bloom-mipmap-combine',\r\n            radianceName,\r\n            bloomMaterial,\r\n            3,\r\n            LoadOp.LOAD,\r\n        );\r\n        combinePass.addTexture(upSampleInfos[upSampleInfos.length - 1].name, 'bloomTexture');\r\n        combinePass.setVec4('bloomParams', this._bloomParams);\r\n        if (cameraConfigs.remainingPasses === 0) {\r\n            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, radianceName);\r\n        } else {\r\n            return combinePass;\r\n        }\r\n    }\r\n\r\n    // Bloom\r\n    private readonly _clearColorTransparentBlack = new Color(0, 0, 0, 0);\r\n    private readonly _bloomParams = new Vec4(0, 0, 0, 0);\r\n    private readonly _bloomTexSize = new Vec4(0, 0, 0, 0);\r\n    private readonly _bloomWidths: Array<number> = [];\r\n    private readonly _bloomHeights: Array<number> = [];\r\n    private readonly _bloomTexNames: Array<string> = [];\r\n\r\n    // Mipmap Bloom\r\n    private readonly _bloomUpSampleTexDescs: Array<RenderTextureDesc> = [];\r\n    private readonly _bloomDownSampleTexDescs: Array<RenderTextureDesc> = [];\r\n    private _prefilterTexDesc: RenderTextureDesc = { name: '', width: 0, height: 0 };\r\n    private _originalColorDesc: RenderTextureDesc = { name: '', width: 0, height: 0 };\r\n}\r\n\r\nexport interface ToneMappingPassConfigs {\r\n    enableToneMapping: boolean;\r\n    enableColorGrading: boolean;\r\n}\r\n\r\nexport class BuiltinToneMappingPassBuilder implements rendering.PipelinePassBuilder {\r\n    getConfigOrder(): number {\r\n        return 0;\r\n    }\r\n    getRenderOrder(): number {\r\n        return 300;\r\n    }\r\n    configCamera(\r\n        camera: Readonly<renderer.scene.Camera>,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs): void {\r\n        const settings = cameraConfigs.settings;\r\n\r\n        cameraConfigs.enableColorGrading\r\n            = settings.colorGrading.enabled\r\n            && !!settings.colorGrading.material\r\n            && !!settings.colorGrading.colorGradingMap;\r\n\r\n        cameraConfigs.enableToneMapping\r\n            = cameraConfigs.enableHDR // From Half to RGBA8\r\n            || cameraConfigs.enableColorGrading; // Color grading\r\n\r\n        if (cameraConfigs.enableToneMapping) {\r\n            ++cameraConfigs.remainingPasses;\r\n        }\r\n    }\r\n    windowResize(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs): void {\r\n        if (cameraConfigs.enableColorGrading) {\r\n            assert(!!cameraConfigs.settings.colorGrading.material);\r\n            cameraConfigs.settings.colorGrading.material.setProperty(\r\n                'colorGradingMap',\r\n                cameraConfigs.settings.colorGrading.colorGradingMap);\r\n        }\r\n    }\r\n    setup(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs,\r\n        camera: renderer.scene.Camera,\r\n        context: PipelineContext,\r\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\r\n        : rendering.BasicRenderPassBuilder | undefined {\r\n        if (!cameraConfigs.enableToneMapping) {\r\n            return prevRenderPass;\r\n        }\r\n\r\n        --cameraConfigs.remainingPasses;\r\n        assert(cameraConfigs.remainingPasses >= 0);\r\n        if (cameraConfigs.remainingPasses === 0) {\r\n            return this._addCopyAndTonemapPass(ppl, pplConfigs, cameraConfigs,\r\n                cameraConfigs.width, cameraConfigs.height,\r\n                context.colorName, cameraConfigs.colorName);\r\n        } else {\r\n            const id = cameraConfigs.renderWindowId;\r\n            const ldrColorPrefix = cameraConfigs.enableShadingScale\r\n                ? `ScaledLdrColor`\r\n                : `LdrColor`;\r\n\r\n            const ldrColorName = getPingPongRenderTarget(context.colorName, ldrColorPrefix, id);\r\n            const radianceName = context.colorName;\r\n            context.colorName = ldrColorName;\r\n\r\n            return this._addCopyAndTonemapPass(ppl, pplConfigs, cameraConfigs,\r\n                cameraConfigs.width, cameraConfigs.height,\r\n                radianceName, ldrColorName);\r\n        }\r\n    }\r\n    private _addCopyAndTonemapPass(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & ToneMappingPassConfigs,\r\n        width: number,\r\n        height: number,\r\n        radianceName: string,\r\n        colorName: string,\r\n    ): rendering.BasicRenderPassBuilder {\r\n        let pass: rendering.BasicRenderPassBuilder;\r\n        const settings = cameraConfigs.settings;\r\n        if (cameraConfigs.enableColorGrading) {\r\n            assert(!!settings.colorGrading.material);\r\n            assert(!!settings.colorGrading.colorGradingMap);\r\n\r\n            const lutTex = settings.colorGrading.colorGradingMap;\r\n            this._colorGradingTexSize.x = lutTex.width;\r\n            this._colorGradingTexSize.y = lutTex.height;\r\n\r\n            const isSquareMap = lutTex.width === lutTex.height;\r\n            if (isSquareMap) {\r\n                pass = ppl.addRenderPass(width, height, 'cc-color-grading-8x8');\r\n            } else {\r\n                pass = ppl.addRenderPass(width, height, 'cc-color-grading-nx1');\r\n            }\r\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\r\n            pass.addTexture(radianceName, 'sceneColorMap');\r\n            pass.setVec2('lutTextureSize', this._colorGradingTexSize);\r\n            pass.setFloat('contribute', settings.colorGrading.contribute);\r\n            pass.addQueue(rendering.QueueHint.OPAQUE)\r\n                .addFullscreenQuad(settings.colorGrading.material, isSquareMap ? 1 : 0);\r\n        } else {\r\n            pass = ppl.addRenderPass(width, height, 'cc-tone-mapping');\r\n            pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\r\n            pass.addTexture(radianceName, 'inputTexture');\r\n            if (settings.toneMapping.material) {\r\n                pass.addQueue(rendering.QueueHint.OPAQUE)\r\n                    .addFullscreenQuad(settings.toneMapping.material, 0);\r\n            } else {\r\n                assert(!!cameraConfigs.copyAndTonemapMaterial);\r\n                pass.addQueue(rendering.QueueHint.OPAQUE)\r\n                    .addFullscreenQuad(cameraConfigs.copyAndTonemapMaterial, 0);\r\n            }\r\n        }\r\n        return pass;\r\n    }\r\n    private readonly _colorGradingTexSize = new Vec2(0, 0);\r\n}\r\n\r\nexport interface FXAAPassConfigs {\r\n    enableFXAA: boolean;\r\n}\r\n\r\nexport class BuiltinFXAAPassBuilder implements rendering.PipelinePassBuilder {\r\n    getConfigOrder(): number {\r\n        return 0;\r\n    }\r\n    getRenderOrder(): number {\r\n        return 400;\r\n    }\r\n    configCamera(\r\n        camera: Readonly<renderer.scene.Camera>,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & FXAAPassConfigs): void {\r\n        cameraConfigs.enableFXAA\r\n            = cameraConfigs.settings.fxaa.enabled\r\n            && !!cameraConfigs.settings.fxaa.material;\r\n        if (cameraConfigs.enableFXAA) {\r\n            ++cameraConfigs.remainingPasses;\r\n        }\r\n    }\r\n    setup(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & FXAAPassConfigs,\r\n        camera: renderer.scene.Camera,\r\n        context: PipelineContext,\r\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\r\n        : rendering.BasicRenderPassBuilder | undefined {\r\n        if (!cameraConfigs.enableFXAA) {\r\n            return prevRenderPass;\r\n        }\r\n        --cameraConfigs.remainingPasses;\r\n        assert(cameraConfigs.remainingPasses >= 0);\r\n\r\n        const id = cameraConfigs.renderWindowId;\r\n        const ldrColorPrefix = cameraConfigs.enableShadingScale\r\n            ? `ScaledLdrColor`\r\n            : `LdrColor`;\r\n        const ldrColorName = getPingPongRenderTarget(context.colorName, ldrColorPrefix, id);\r\n\r\n        assert(!!cameraConfigs.settings.fxaa.material);\r\n        if (cameraConfigs.remainingPasses === 0) {\r\n            if (cameraConfigs.enableShadingScale) {\r\n                this._addFxaaPass(ppl, pplConfigs,\r\n                    cameraConfigs.settings.fxaa.material,\r\n                    cameraConfigs.width,\r\n                    cameraConfigs.height,\r\n                    context.colorName,\r\n                    ldrColorName);\r\n                return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, ldrColorName);\r\n            } else {\r\n                assert(cameraConfigs.width === cameraConfigs.nativeWidth);\r\n                assert(cameraConfigs.height === cameraConfigs.nativeHeight);\r\n                return this._addFxaaPass(ppl, pplConfigs,\r\n                    cameraConfigs.settings.fxaa.material,\r\n                    cameraConfigs.width,\r\n                    cameraConfigs.height,\r\n                    context.colorName,\r\n                    cameraConfigs.colorName);\r\n            }\r\n        } else {\r\n            const inputColorName = context.colorName;\r\n            context.colorName = ldrColorName;\r\n            const lastPass = this._addFxaaPass(ppl, pplConfigs,\r\n                cameraConfigs.settings.fxaa.material,\r\n                cameraConfigs.width,\r\n                cameraConfigs.height,\r\n                inputColorName,\r\n                ldrColorName);\r\n            return lastPass;\r\n        }\r\n    }\r\n    private _addFxaaPass(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        fxaaMaterial: Material,\r\n        width: number,\r\n        height: number,\r\n        ldrColorName: string,\r\n        colorName: string,\r\n    ): rendering.BasicRenderPassBuilder {\r\n        this._fxaaParams.x = width;\r\n        this._fxaaParams.y = height;\r\n        this._fxaaParams.z = 1 / width;\r\n        this._fxaaParams.w = 1 / height;\r\n\r\n        const pass = ppl.addRenderPass(width, height, 'cc-fxaa');\r\n        pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\r\n        pass.addTexture(ldrColorName, 'sceneColorMap');\r\n        pass.setVec4('texSize', this._fxaaParams);\r\n        pass.addQueue(rendering.QueueHint.OPAQUE)\r\n            .addFullscreenQuad(fxaaMaterial, 0);\r\n        return pass;\r\n    }\r\n    // FXAA\r\n    private readonly _fxaaParams = new Vec4(0, 0, 0, 0);\r\n}\r\n\r\nexport interface FSRPassConfigs {\r\n    enableFSR: boolean;\r\n}\r\n\r\nexport class BuiltinFsrPassBuilder implements rendering.PipelinePassBuilder {\r\n    getConfigOrder(): number {\r\n        return 0;\r\n    }\r\n    getRenderOrder(): number {\r\n        return 500;\r\n    }\r\n    configCamera(\r\n        camera: Readonly<renderer.scene.Camera>,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & FSRPassConfigs): void {\r\n        // FSR (Depend on Shading scale)\r\n        cameraConfigs.enableFSR = cameraConfigs.settings.fsr.enabled\r\n            && !!cameraConfigs.settings.fsr.material\r\n            && cameraConfigs.enableShadingScale\r\n            && cameraConfigs.shadingScale < 1.0;\r\n\r\n        if (cameraConfigs.enableFSR) {\r\n            ++cameraConfigs.remainingPasses;\r\n        }\r\n    }\r\n    setup(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & FSRPassConfigs,\r\n        camera: renderer.scene.Camera,\r\n        context: PipelineContext,\r\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\r\n        : rendering.BasicRenderPassBuilder | undefined {\r\n        if (!cameraConfigs.enableFSR) {\r\n            return prevRenderPass;\r\n        }\r\n        --cameraConfigs.remainingPasses;\r\n\r\n        const inputColorName = context.colorName;\r\n        const outputColorName\r\n            = cameraConfigs.remainingPasses === 0\r\n                ? cameraConfigs.colorName\r\n                : getPingPongRenderTarget(context.colorName, 'UiColor', cameraConfigs.renderWindowId);\r\n        context.colorName = outputColorName;\r\n\r\n        assert(!!cameraConfigs.settings.fsr.material);\r\n        return this._addFsrPass(ppl, pplConfigs, cameraConfigs,\r\n            cameraConfigs.settings,\r\n            cameraConfigs.settings.fsr.material,\r\n            cameraConfigs.renderWindowId,\r\n            cameraConfigs.width,\r\n            cameraConfigs.height,\r\n            inputColorName,\r\n            cameraConfigs.nativeWidth,\r\n            cameraConfigs.nativeHeight,\r\n            outputColorName);\r\n    }\r\n    private _addFsrPass(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & FSRPassConfigs,\r\n        settings: PipelineSettings,\r\n        fsrMaterial: Material,\r\n        id: number,\r\n        width: number,\r\n        height: number,\r\n        inputColorName: string,\r\n        nativeWidth: number,\r\n        nativeHeight: number,\r\n        outputColorName: string,\r\n    ): rendering.BasicRenderPassBuilder {\r\n        this._fsrTexSize.x = width;\r\n        this._fsrTexSize.y = height;\r\n        this._fsrTexSize.z = nativeWidth;\r\n        this._fsrTexSize.w = nativeHeight;\r\n        this._fsrParams.x = clamp(1.0 - settings.fsr.sharpness, 0.02, 0.98);\r\n\r\n        const uiColorPrefix = 'UiColor';\r\n\r\n        const fsrColorName = getPingPongRenderTarget(outputColorName, uiColorPrefix, id);\r\n\r\n        const easuPass = ppl.addRenderPass(nativeWidth, nativeHeight, 'cc-fsr-easu');\r\n        easuPass.addRenderTarget(fsrColorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\r\n        easuPass.addTexture(inputColorName, 'outputResultMap');\r\n        easuPass.setVec4('fsrTexSize', this._fsrTexSize);\r\n        easuPass\r\n            .addQueue(rendering.QueueHint.OPAQUE)\r\n            .addFullscreenQuad(fsrMaterial, 0);\r\n\r\n        const rcasPass = ppl.addRenderPass(nativeWidth, nativeHeight, 'cc-fsr-rcas');\r\n        rcasPass.addRenderTarget(outputColorName, LoadOp.CLEAR, StoreOp.STORE, sClearColorTransparentBlack);\r\n        rcasPass.addTexture(fsrColorName, 'outputResultMap');\r\n        rcasPass.setVec4('fsrTexSize', this._fsrTexSize);\r\n        rcasPass.setVec4('fsrParams', this._fsrParams);\r\n        rcasPass\r\n            .addQueue(rendering.QueueHint.OPAQUE)\r\n            .addFullscreenQuad(fsrMaterial, 1);\r\n\r\n        return rcasPass;\r\n    }\r\n    // FSR\r\n    private readonly _fsrParams = new Vec4(0, 0, 0, 0);\r\n    private readonly _fsrTexSize = new Vec4(0, 0, 0, 0);\r\n}\r\n\r\nexport class BuiltinUiPassBuilder implements rendering.PipelinePassBuilder {\r\n    getConfigOrder(): number {\r\n        return 0;\r\n    }\r\n    getRenderOrder(): number {\r\n        return 1000;\r\n    }\r\n    setup(\r\n        ppl: rendering.BasicPipeline,\r\n        pplConfigs: Readonly<PipelineConfigs>,\r\n        cameraConfigs: CameraConfigs & FSRPassConfigs,\r\n        camera: renderer.scene.Camera,\r\n        context: PipelineContext,\r\n        prevRenderPass?: rendering.BasicRenderPassBuilder)\r\n        : rendering.BasicRenderPassBuilder | undefined {\r\n        assert(!!prevRenderPass);\r\n\r\n        let flags = rendering.SceneFlags.UI;\r\n        if (cameraConfigs.enableProfiler) {\r\n            flags |= rendering.SceneFlags.PROFILER;\r\n            prevRenderPass.showStatistics = true;\r\n        }\r\n        prevRenderPass\r\n            .addQueue(rendering.QueueHint.BLEND, 'default', 'default')\r\n            .addScene(camera, flags);\r\n\r\n        return prevRenderPass;\r\n    }\r\n}\r\n\r\nif (rendering) {\r\n\r\n    const { QueueHint, SceneFlags } = rendering;\r\n\r\n    class BuiltinPipelineBuilder implements rendering.PipelineBuilder {\r\n        private readonly _pipelineEvent: PipelineEventProcessor = cclegacy.director.root.pipelineEvent as PipelineEventProcessor;\r\n        private readonly _forwardPass = new BuiltinForwardPassBuilder();\r\n        private readonly _bloomPass = new BuiltinBloomPassBuilder();\r\n        private readonly _toneMappingPass = new BuiltinToneMappingPassBuilder();\r\n        private readonly _fxaaPass = new BuiltinFXAAPassBuilder();\r\n        private readonly _fsrPass = new BuiltinFsrPassBuilder();\r\n        private readonly _uiPass = new BuiltinUiPassBuilder();\r\n        // Internal cached resources\r\n        private readonly _clearColor = new Color(0, 0, 0, 1);\r\n        private readonly _viewport = new Viewport();\r\n        private readonly _configs = new PipelineConfigs();\r\n        private readonly _cameraConfigs = new CameraConfigs();\r\n        // Materials\r\n        private readonly _copyAndTonemapMaterial = new Material();\r\n\r\n        // Internal States\r\n        private _initialized = false; // TODO(zhouzhenglong): Make default effect asset loading earlier and remove this flag\r\n        private _passBuilders: rendering.PipelinePassBuilder[] = [];\r\n\r\n        private _setupPipelinePreview(\r\n            camera: renderer.scene.Camera,\r\n            cameraConfigs: CameraConfigs) {\r\n            const isEditorView: boolean\r\n                = camera.cameraUsage === CameraUsage.SCENE_VIEW\r\n                || camera.cameraUsage === CameraUsage.PREVIEW;\r\n\r\n            if (isEditorView) {\r\n                const editorSettings = rendering.getEditorPipelineSettings() as PipelineSettings | null;\r\n                if (editorSettings) {\r\n                    cameraConfigs.settings = editorSettings;\r\n                } else {\r\n                    cameraConfigs.settings = defaultSettings;\r\n                }\r\n            } else {\r\n                if (camera.pipelineSettings) {\r\n                    cameraConfigs.settings = camera.pipelineSettings as PipelineSettings;\r\n                } else {\r\n                    cameraConfigs.settings = defaultSettings;\r\n                }\r\n            }\r\n        }\r\n\r\n        private _preparePipelinePasses(cameraConfigs: CameraConfigs): void {\r\n            const passBuilders = this._passBuilders;\r\n            passBuilders.length = 0;\r\n\r\n            const settings = cameraConfigs.settings as PipelineSettings2;\r\n            if (settings._passes) {\r\n                for (const pass of settings._passes) {\r\n                    passBuilders.push(pass);\r\n                }\r\n                assert(passBuilders.length === settings._passes.length);\r\n            }\r\n\r\n            passBuilders.push(this._forwardPass);\r\n\r\n            if (settings.bloom.enabled) {\r\n                passBuilders.push(this._bloomPass);\r\n            }\r\n\r\n            passBuilders.push(this._toneMappingPass);\r\n\r\n            if (settings.fxaa.enabled) {\r\n                passBuilders.push(this._fxaaPass);\r\n            }\r\n\r\n            if (settings.fsr.enabled) {\r\n                passBuilders.push(this._fsrPass);\r\n            }\r\n            passBuilders.push(this._uiPass);\r\n        }\r\n\r\n        private _setupBuiltinCameraConfigs(\r\n            ppl: rendering.BasicPipeline,\r\n            camera: renderer.scene.Camera,\r\n            pipelineConfigs: PipelineConfigs,\r\n            cameraConfigs: CameraConfigs\r\n        ) {\r\n            const window = camera.window;\r\n            const isMainGameWindow: boolean = camera.cameraUsage === CameraUsage.GAME && !!window.swapchain;\r\n            const isGameView = isMainGameWindow || camera.cameraUsage === CameraUsage.GAME_VIEW;\r\n\r\n            // Window\r\n            cameraConfigs.isMainGameWindow = isMainGameWindow;\r\n            cameraConfigs.renderWindowId = window.renderWindowId;\r\n\r\n            // Camera\r\n            cameraConfigs.colorName = window.colorName;\r\n            cameraConfigs.depthStencilName = window.depthStencilName;\r\n\r\n            // Pipeline\r\n            cameraConfigs.enableFullPipeline = (camera.visibility & (Layers.Enum.DEFAULT)) !== 0;\r\n            cameraConfigs.enableProfiler = ppl.profiler && isGameView;\r\n            cameraConfigs.remainingPasses = 0;\r\n\r\n            // Shading scale\r\n            cameraConfigs.shadingScale = cameraConfigs.settings.shadingScale;\r\n            cameraConfigs.enableShadingScale = cameraConfigs.settings.enableShadingScale\r\n                && cameraConfigs.shadingScale !== 1.0;\r\n\r\n            cameraConfigs.nativeWidth = Math.max(Math.floor(window.width), 1);\r\n            cameraConfigs.nativeHeight = Math.max(Math.floor(window.height), 1);\r\n\r\n            cameraConfigs.width = cameraConfigs.enableShadingScale\r\n                ? Math.max(Math.floor(cameraConfigs.nativeWidth * cameraConfigs.shadingScale), 1)\r\n                : cameraConfigs.nativeWidth;\r\n            cameraConfigs.height = cameraConfigs.enableShadingScale\r\n                ? Math.max(Math.floor(cameraConfigs.nativeHeight * cameraConfigs.shadingScale), 1)\r\n                : cameraConfigs.nativeHeight;\r\n\r\n            // Radiance\r\n            cameraConfigs.enableHDR = cameraConfigs.enableFullPipeline\r\n                && pipelineConfigs.useFloatOutput;\r\n            cameraConfigs.radianceFormat = cameraConfigs.enableHDR\r\n                ? gfx.Format.RGBA16F : gfx.Format.RGBA8;\r\n\r\n            // Tone Mapping\r\n            cameraConfigs.copyAndTonemapMaterial = this._copyAndTonemapMaterial;\r\n\r\n            // Depth\r\n            cameraConfigs.enableStoreSceneDepth = false;\r\n        }\r\n\r\n        private _setupCameraConfigs(\r\n            ppl: rendering.BasicPipeline,\r\n            camera: renderer.scene.Camera,\r\n            pipelineConfigs: PipelineConfigs,\r\n            cameraConfigs: CameraConfigs\r\n        ): void {\r\n            this._setupPipelinePreview(camera, cameraConfigs);\r\n\r\n            this._preparePipelinePasses(cameraConfigs);\r\n\r\n            sortPipelinePassBuildersByConfigOrder(this._passBuilders);\r\n\r\n            this._setupBuiltinCameraConfigs(ppl, camera, pipelineConfigs, cameraConfigs);\r\n\r\n            for (const builder of this._passBuilders) {\r\n                if (builder.configCamera) {\r\n                    builder.configCamera(camera, pipelineConfigs, cameraConfigs);\r\n                }\r\n            }\r\n        }\r\n\r\n        // ----------------------------------------------------------------\r\n        // Interface\r\n        // ----------------------------------------------------------------\r\n        windowResize(\r\n            ppl: rendering.BasicPipeline,\r\n            window: renderer.RenderWindow,\r\n            camera: renderer.scene.Camera,\r\n            nativeWidth: number,\r\n            nativeHeight: number,\r\n        ): void {\r\n            setupPipelineConfigs(ppl, this._configs);\r\n\r\n            this._setupCameraConfigs(ppl, camera, this._configs, this._cameraConfigs);\r\n\r\n            // Render Window (UI)\r\n            const id = window.renderWindowId;\r\n\r\n            ppl.addRenderWindow(this._cameraConfigs.colorName,\r\n                Format.RGBA8, nativeWidth, nativeHeight, window,\r\n                this._cameraConfigs.depthStencilName);\r\n\r\n            const width = this._cameraConfigs.width;\r\n            const height = this._cameraConfigs.height;\r\n\r\n            if (this._cameraConfigs.enableShadingScale) {\r\n                ppl.addDepthStencil(`ScaledSceneDepth_${id}`, Format.DEPTH_STENCIL, width, height);\r\n                ppl.addRenderTarget(`ScaledRadiance0_${id}`, this._cameraConfigs.radianceFormat, width, height);\r\n                ppl.addRenderTarget(`ScaledRadiance1_${id}`, this._cameraConfigs.radianceFormat, width, height);\r\n                ppl.addRenderTarget(`ScaledLdrColor0_${id}`, Format.RGBA8, width, height);\r\n                ppl.addRenderTarget(`ScaledLdrColor1_${id}`, Format.RGBA8, width, height);\r\n            } else {\r\n                ppl.addDepthStencil(`SceneDepth_${id}`, Format.DEPTH_STENCIL, width, height);\r\n                ppl.addRenderTarget(`Radiance0_${id}`, this._cameraConfigs.radianceFormat, width, height);\r\n                ppl.addRenderTarget(`Radiance1_${id}`, this._cameraConfigs.radianceFormat, width, height);\r\n                ppl.addRenderTarget(`LdrColor0_${id}`, Format.RGBA8, width, height);\r\n                ppl.addRenderTarget(`LdrColor1_${id}`, Format.RGBA8, width, height);\r\n            }\r\n            ppl.addRenderTarget(`UiColor0_${id}`, Format.RGBA8, nativeWidth, nativeHeight);\r\n            ppl.addRenderTarget(`UiColor1_${id}`, Format.RGBA8, nativeWidth, nativeHeight);\r\n\r\n            for (const builder of this._passBuilders) {\r\n                if (builder.windowResize) {\r\n                    builder.windowResize(ppl, this._configs, this._cameraConfigs, window, camera, nativeWidth, nativeHeight);\r\n                }\r\n            }\r\n        }\r\n        setup(cameras: renderer.scene.Camera[], ppl: rendering.BasicPipeline): void {\r\n            // TODO(zhouzhenglong): Make default effect asset loading earlier and remove _initMaterials\r\n            if (this._initMaterials(ppl)) {\r\n                return;\r\n            }\r\n\r\n            // Render cameras\r\n            // log(`==================== One Frame ====================`);\r\n            for (const camera of cameras) {\r\n                // Skip invalid camera\r\n                if (!camera.scene || !camera.window) {\r\n                    continue;\r\n                }\r\n                // Setup camera configs\r\n                this._setupCameraConfigs(ppl, camera, this._configs, this._cameraConfigs);\r\n                // log(`Setup camera: ${camera.node!.name}, window: ${camera.window.renderWindowId}, isFull: ${this._cameraConfigs.enableFullPipeline}, `\r\n                //     + `size: ${camera.window.width}x${camera.window.height}`);\r\n\r\n                this._pipelineEvent.emit(PipelineEventType.RENDER_CAMERA_BEGIN, camera);\r\n\r\n                // Build pipeline\r\n                if (this._cameraConfigs.enableFullPipeline) {\r\n                    this._buildForwardPipeline(ppl, camera, camera.scene, this._passBuilders);\r\n                } else {\r\n                    this._buildSimplePipeline(ppl, camera);\r\n                }\r\n\r\n                this._pipelineEvent.emit(PipelineEventType.RENDER_CAMERA_END, camera);\r\n            }\r\n        }\r\n        // ----------------------------------------------------------------\r\n        // Pipelines\r\n        // ----------------------------------------------------------------\r\n        private _buildSimplePipeline(\r\n            ppl: rendering.BasicPipeline,\r\n            camera: renderer.scene.Camera,\r\n        ): void {\r\n            const width = Math.max(Math.floor(camera.window.width), 1);\r\n            const height = Math.max(Math.floor(camera.window.height), 1);\r\n            const colorName = this._cameraConfigs.colorName;\r\n            const depthStencilName = this._cameraConfigs.depthStencilName;\r\n\r\n            const viewport = camera.viewport;  // Reduce C++/TS interop\r\n            this._viewport.left = Math.round(viewport.x * width);\r\n            this._viewport.top = Math.round(viewport.y * height);\r\n            // Here we must use camera.viewport.width instead of camera.viewport.z, which\r\n            // is undefined on native platform. The same as camera.viewport.height.\r\n            this._viewport.width = Math.max(Math.round(viewport.width * width), 1);\r\n            this._viewport.height = Math.max(Math.round(viewport.height * height), 1);\r\n\r\n            const clearColor = camera.clearColor;  // Reduce C++/TS interop\r\n            this._clearColor.x = clearColor.x;\r\n            this._clearColor.y = clearColor.y;\r\n            this._clearColor.z = clearColor.z;\r\n            this._clearColor.w = clearColor.w;\r\n\r\n            const pass = ppl.addRenderPass(width, height, 'default');\r\n\r\n            // bind output render target\r\n            if (forwardNeedClearColor(camera)) {\r\n                pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, this._clearColor);\r\n            } else {\r\n                pass.addRenderTarget(colorName, LoadOp.LOAD, StoreOp.STORE);\r\n            }\r\n\r\n            // bind depth stencil buffer\r\n            if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {\r\n                pass.addDepthStencil(\r\n                    depthStencilName,\r\n                    LoadOp.CLEAR,\r\n                    StoreOp.DISCARD,\r\n                    camera.clearDepth,\r\n                    camera.clearStencil,\r\n                    camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,\r\n                );\r\n            } else {\r\n                pass.addDepthStencil(depthStencilName, LoadOp.LOAD, StoreOp.DISCARD);\r\n            }\r\n\r\n            pass.setViewport(this._viewport);\r\n\r\n            // The opaque queue is used for Reflection probe preview\r\n            pass.addQueue(QueueHint.OPAQUE)\r\n                .addScene(camera, SceneFlags.OPAQUE);\r\n\r\n            // The blend queue is used for UI and Gizmos\r\n            let flags = SceneFlags.BLEND | SceneFlags.UI;\r\n            if (this._cameraConfigs.enableProfiler) {\r\n                flags |= SceneFlags.PROFILER;\r\n                pass.showStatistics = true;\r\n            }\r\n            pass.addQueue(QueueHint.BLEND)\r\n                .addScene(camera, flags);\r\n        }\r\n\r\n        private _buildForwardPipeline(\r\n            ppl: rendering.BasicPipeline,\r\n            camera: renderer.scene.Camera,\r\n            scene: renderer.RenderScene,\r\n            passBuilders: rendering.PipelinePassBuilder[],\r\n        ): void {\r\n            sortPipelinePassBuildersByRenderOrder(passBuilders);\r\n\r\n            const context: PipelineContext = {\r\n                colorName: '',\r\n                depthStencilName: '',\r\n            };\r\n\r\n            let lastPass: rendering.BasicRenderPassBuilder | undefined = undefined;\r\n\r\n            for (const builder of passBuilders) {\r\n                if (builder.setup) {\r\n                    lastPass = builder.setup(ppl, this._configs, this._cameraConfigs,\r\n                        camera, context, lastPass);\r\n                }\r\n            }\r\n\r\n            assert(this._cameraConfigs.remainingPasses === 0);\r\n        }\r\n\r\n        private _initMaterials(ppl: rendering.BasicPipeline): number {\r\n            if (this._initialized) {\r\n                return 0;\r\n            }\r\n\r\n            setupPipelineConfigs(ppl, this._configs);\r\n\r\n            // When add new effect asset, please add its uuid to the dependentAssets in cc.config.json.\r\n            this._copyAndTonemapMaterial._uuid = `builtin-pipeline-tone-mapping-material`;\r\n            this._copyAndTonemapMaterial.initialize({ effectName: 'pipeline/post-process/tone-mapping' });\r\n\r\n            if (this._copyAndTonemapMaterial.effectAsset) {\r\n                this._initialized = true;\r\n            }\r\n\r\n            return this._initialized ? 0 : 1;\r\n        }\r\n    }\r\n\r\n    rendering.setCustomPipeline('Builtin', new BuiltinPipelineBuilder());\r\n\r\n} // if (rendering)\r\n"]}