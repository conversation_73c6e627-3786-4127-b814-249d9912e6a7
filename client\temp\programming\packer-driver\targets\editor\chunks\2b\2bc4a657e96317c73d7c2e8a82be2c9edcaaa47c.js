System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, LoginType, LoginState, NotifyType, SoldierCamp, SoldierState, MapNodeType, DragTouchType;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "39e2efoEuhL07WTowHKWz3H", "Enums", undefined);

      // 登陆类型
      _export("LoginType", LoginType = /*#__PURE__*/function (LoginType) {
        LoginType["NONE"] = "none";
        LoginType["GUEST"] = "guest";
        LoginType["ACCOUNT"] = "account";
        LoginType["WX"] = "wx";
        LoginType["GOOGLE"] = "google";
        LoginType["APPLE"] = "apple";
        LoginType["FACEBOOK"] = "facebook";
        LoginType["TWITTER"] = "twitter";
        LoginType["LINE"] = "line";
        return LoginType;
      }(LoginType || {})); // 登录状态


      _export("LoginState", LoginState = /*#__PURE__*/function (LoginState) {
        LoginState[LoginState["SUCCEED"] = 0] = "SUCCEED";
        LoginState[LoginState["FAILURE"] = 1] = "FAILURE";
        LoginState[LoginState["NOT_ACCOUNT_TOKEN"] = 2] = "NOT_ACCOUNT_TOKEN";
        LoginState[LoginState["VERSION_TOOLOW"] = 3] = "VERSION_TOOLOW";
        LoginState[LoginState["VERSION_TOOTALL"] = 4] = "VERSION_TOOTALL";
        LoginState[LoginState["BANACCOUNT_TIME"] = 5] = "BANACCOUNT_TIME";
        LoginState[LoginState["QUEUE_UP"] = 6] = "QUEUE_UP";
        LoginState[LoginState["ENTER_GAME"] = 7] = "ENTER_GAME";
        return LoginState;
      }(LoginState || {})); // 服务器的通知类型


      _export("NotifyType", NotifyType = /*#__PURE__*/function (NotifyType) {
        NotifyType[NotifyType["NONE"] = 0] = "NONE";
        return NotifyType;
      }(NotifyType || {})); // 阵营


      _export("SoldierCamp", SoldierCamp = /*#__PURE__*/function (SoldierCamp) {
        SoldierCamp[SoldierCamp["NONE"] = 0] = "NONE";
        SoldierCamp[SoldierCamp["FRIENDLY"] = 1] = "FRIENDLY";
        SoldierCamp[SoldierCamp["ENEMY"] = 2] = "ENEMY";
        return SoldierCamp;
      }(SoldierCamp || {})); // 武将状态


      _export("SoldierState", SoldierState = /*#__PURE__*/function (SoldierState) {
        SoldierState[SoldierState["NONE"] = 0] = "NONE";
        SoldierState[SoldierState["STAND"] = 1] = "STAND";
        SoldierState[SoldierState["ATTACK"] = 2] = "ATTACK";
        SoldierState[SoldierState["HIT"] = 3] = "HIT";
        return SoldierState;
      }(SoldierState || {})); // 地图节点类型


      _export("MapNodeType", MapNodeType = /*#__PURE__*/function (MapNodeType) {
        MapNodeType[MapNodeType["NONE"] = 0] = "NONE";
        MapNodeType[MapNodeType["BATTLE"] = 1] = "BATTLE";
        MapNodeType[MapNodeType["SHOP"] = 2] = "SHOP";
        MapNodeType[MapNodeType["EVENT"] = 3] = "EVENT";
        MapNodeType[MapNodeType["TREASURE"] = 4] = "TREASURE";
        MapNodeType[MapNodeType["PLAYER"] = 5] = "PLAYER";
        return MapNodeType;
      }(MapNodeType || {})); // 拖拽触摸类型


      _export("DragTouchType", DragTouchType = /*#__PURE__*/function (DragTouchType) {
        DragTouchType[DragTouchType["NONE"] = 0] = "NONE";
        DragTouchType[DragTouchType["DRAG_BEGIN"] = 1] = "DRAG_BEGIN";
        DragTouchType[DragTouchType["DRAG_MOVE"] = 2] = "DRAG_MOVE";
        DragTouchType[DragTouchType["CLICK"] = 3] = "CLICK";
        DragTouchType[DragTouchType["END"] = 4] = "END";
        return DragTouchType;
      }(DragTouchType || {}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2bc4a657e96317c73d7c2e8a82be2c9edcaaa47c.js.map