{"version": 3, "sources": ["file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts"], "names": ["makeMSAA", "enabled", "sampleCount", "SampleCount", "X4", "fillRequiredMSAA", "value", "undefined", "makeHBAO", "radiusScale", "angleBiasDegree", "blurSharpness", "aoSaturation", "need<PERSON><PERSON><PERSON>", "fillRequiredHBAO", "makeBloom", "type", "BloomType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "material", "kawaseFilterMaterial", "mipmapFilterMaterial", "enableAlphaMask", "iterations", "threshold", "intensity", "fillRequiredBloom", "makeColorGrading", "contribute", "colorGradingMap", "fillRequiredColorGrading", "makeFSR", "sharpness", "fillRequiredFSR", "makeFXAA", "fillRequiredFXAA", "makeToneMapping", "fillRequiredToneMapping", "makePipelineSettings", "msaa", "enableShadingScale", "shadingScale", "bloom", "toneMapping", "colorGrading", "fsr", "fxaa", "fillRequiredPipelineSettings", "ccenum", "gfx"], "mappings": ";;;;;AAwCO,WAASA,QAAT,GAA0B;AAC7B,WAAO;AACHC,MAAAA,OAAO,EAAE,KADN;AAEHC,MAAAA,WAAW,EAAEC,WAAW,CAACC;AAFtB,KAAP;AAIH;;AAEM,WAASC,gBAAT,CAA0BC,KAA1B,EAA6C;AAChD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACJ,WAAN,KAAsBK,SAA1B,EAAqC;AACjCD,MAAAA,KAAK,CAACJ,WAAN,GAAoBC,WAAW,CAACC,EAAhC;AACH;AACJ;;AAqBM,WAASI,QAAT,GAA0B;AAC7B,WAAO;AACHP,MAAAA,OAAO,EAAE,KADN;AAEHQ,MAAAA,WAAW,EAAE,CAFV;AAGHC,MAAAA,eAAe,EAAE,EAHd;AAIHC,MAAAA,aAAa,EAAE,CAJZ;AAKHC,MAAAA,YAAY,EAAE,CALX;AAMHC,MAAAA,QAAQ,EAAE;AANP,KAAP;AAQH;;AAEM,WAASC,gBAAT,CAA0BR,KAA1B,EAA6C;AAChD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACG,WAAN,KAAsBF,SAA1B,EAAqC;AACjCD,MAAAA,KAAK,CAACG,WAAN,GAAoB,CAApB;AACH;;AACD,QAAIH,KAAK,CAACI,eAAN,KAA0BH,SAA9B,EAAyC;AACrCD,MAAAA,KAAK,CAACI,eAAN,GAAwB,EAAxB;AACH;;AACD,QAAIJ,KAAK,CAACK,aAAN,KAAwBJ,SAA5B,EAAuC;AACnCD,MAAAA,KAAK,CAACK,aAAN,GAAsB,CAAtB;AACH;;AACD,QAAIL,KAAK,CAACM,YAAN,KAAuBL,SAA3B,EAAsC;AAClCD,MAAAA,KAAK,CAACM,YAAN,GAAqB,CAArB;AACH;;AACD,QAAIN,KAAK,CAACO,QAAN,KAAmBN,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACO,QAAN,GAAiB,KAAjB;AACH;AACJ;;AAqBM,WAASE,SAAT,GAA4B;AAC/B,WAAO;AACHd,MAAAA,OAAO,EAAE,KADN;AAEHe,MAAAA,IAAI,EAAEC,SAAS,CAACC,gBAFb;AAGHC,MAAAA,QAAQ,EAAE,IAHP;AAIHC,MAAAA,oBAAoB,EAAE,IAJnB;AAKHC,MAAAA,oBAAoB,EAAE,IALnB;AAMHC,MAAAA,eAAe,EAAE,KANd;AAOHC,MAAAA,UAAU,EAAE,CAPT;AAQHC,MAAAA,SAAS,EAAE,GARR;AASHC,MAAAA,SAAS,EAAE;AATR,KAAP;AAWH;;AAEM,WAASC,iBAAT,CAA2BpB,KAA3B,EAA+C;AAClD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACU,IAAN,KAAeT,SAAnB,EAA8B;AAC1BD,MAAAA,KAAK,CAACU,IAAN,GAAaC,SAAS,CAACC,gBAAvB;AACH;;AACD,QAAIZ,KAAK,CAACa,QAAN,KAAmBZ,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACa,QAAN,GAAiB,IAAjB;AACH;;AACD,QAAIb,KAAK,CAACc,oBAAN,KAA+Bb,SAAnC,EAA8C;AAC1CD,MAAAA,KAAK,CAACc,oBAAN,GAA6Bd,KAAK,CAACa,QAAN,IAAkB,IAA/C;AACH;;AACD,QAAIb,KAAK,CAACe,oBAAN,KAA+Bd,SAAnC,EAA8C;AAC1CD,MAAAA,KAAK,CAACe,oBAAN,GAA6B,IAA7B;AACH;;AACD,QAAIf,KAAK,CAACgB,eAAN,KAA0Bf,SAA9B,EAAyC;AACrCD,MAAAA,KAAK,CAACgB,eAAN,GAAwB,KAAxB;AACH;;AACD,QAAIhB,KAAK,CAACiB,UAAN,KAAqBhB,SAAzB,EAAoC;AAChCD,MAAAA,KAAK,CAACiB,UAAN,GAAmB,CAAnB;AACH;;AACD,QAAIjB,KAAK,CAACkB,SAAN,KAAoBjB,SAAxB,EAAmC;AAC/BD,MAAAA,KAAK,CAACkB,SAAN,GAAkB,GAAlB;AACH;;AACD,QAAIlB,KAAK,CAACmB,SAAN,KAAoBlB,SAAxB,EAAmC;AAC/BD,MAAAA,KAAK,CAACmB,SAAN,GAAkB,CAAlB;AACH;AACJ;;AAUM,WAASE,gBAAT,GAA0C;AAC7C,WAAO;AACH1B,MAAAA,OAAO,EAAE,KADN;AAEHkB,MAAAA,QAAQ,EAAE,IAFP;AAGHS,MAAAA,UAAU,EAAE,CAHT;AAIHC,MAAAA,eAAe,EAAE;AAJd,KAAP;AAMH;;AAEM,WAASC,wBAAT,CAAkCxB,KAAlC,EAA6D;AAChE,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACa,QAAN,KAAmBZ,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACa,QAAN,GAAiB,IAAjB;AACH;;AACD,QAAIb,KAAK,CAACsB,UAAN,KAAqBrB,SAAzB,EAAoC;AAChCD,MAAAA,KAAK,CAACsB,UAAN,GAAmB,CAAnB;AACH;;AACD,QAAItB,KAAK,CAACuB,eAAN,KAA0BtB,SAA9B,EAAyC;AACrCD,MAAAA,KAAK,CAACuB,eAAN,GAAwB,IAAxB;AACH;AACJ;;AASM,WAASE,OAAT,GAAwB;AAC3B,WAAO;AACH9B,MAAAA,OAAO,EAAE,KADN;AAEHkB,MAAAA,QAAQ,EAAE,IAFP;AAGHa,MAAAA,SAAS,EAAE;AAHR,KAAP;AAKH;;AAEM,WAASC,eAAT,CAAyB3B,KAAzB,EAA2C;AAC9C,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACa,QAAN,KAAmBZ,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACa,QAAN,GAAiB,IAAjB;AACH;;AACD,QAAIb,KAAK,CAAC0B,SAAN,KAAoBzB,SAAxB,EAAmC;AAC/BD,MAAAA,KAAK,CAAC0B,SAAN,GAAkB,GAAlB;AACH;AACJ;;AAQM,WAASE,QAAT,GAA0B;AAC7B,WAAO;AACHjC,MAAAA,OAAO,EAAE,KADN;AAEHkB,MAAAA,QAAQ,EAAE;AAFP,KAAP;AAIH;;AAEM,WAASgB,gBAAT,CAA0B7B,KAA1B,EAA6C;AAChD,QAAIA,KAAK,CAACL,OAAN,KAAkBM,SAAtB,EAAiC;AAC7BD,MAAAA,KAAK,CAACL,OAAN,GAAgB,KAAhB;AACH;;AACD,QAAIK,KAAK,CAACa,QAAN,KAAmBZ,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACa,QAAN,GAAiB,IAAjB;AACH;AACJ;;AAOM,WAASiB,eAAT,GAAwC;AAC3C,WAAO;AACHjB,MAAAA,QAAQ,EAAE;AADP,KAAP;AAGH;;AAEM,WAASkB,uBAAT,CAAiC/B,KAAjC,EAA2D;AAC9D,QAAIA,KAAK,CAACa,QAAN,KAAmBZ,SAAvB,EAAkC;AAC9BD,MAAAA,KAAK,CAACa,QAAN,GAAiB,IAAjB;AACH;AACJ;;AAcM,WAASmB,oBAAT,GAAkD;AACrD,WAAO;AACHC,MAAAA,IAAI,EAAEvC,QAAQ,EADX;AAEHwC,MAAAA,kBAAkB,EAAE,KAFjB;AAGHC,MAAAA,YAAY,EAAE,GAHX;AAIHC,MAAAA,KAAK,EAAE3B,SAAS,EAJb;AAKH4B,MAAAA,WAAW,EAAEP,eAAe,EALzB;AAMHQ,MAAAA,YAAY,EAAEjB,gBAAgB,EAN3B;AAOHkB,MAAAA,GAAG,EAAEd,OAAO,EAPT;AAQHe,MAAAA,IAAI,EAAEZ,QAAQ;AARX,KAAP;AAUH;;AAEM,WAASa,4BAAT,CAAsCzC,KAAtC,EAAqE;AACxE,QAAI,CAACA,KAAK,CAACiC,IAAX,EAAiB;AACZjC,MAAAA,KAAK,CAACiC,IAAP,GAAuBvC,QAAQ,EAA/B;AACH,KAFD,MAEO;AACHK,MAAAA,gBAAgB,CAACC,KAAK,CAACiC,IAAP,CAAhB;AACH;;AACD,QAAIjC,KAAK,CAACkC,kBAAN,KAA6BjC,SAAjC,EAA4C;AACxCD,MAAAA,KAAK,CAACkC,kBAAN,GAA2B,KAA3B;AACH;;AACD,QAAIlC,KAAK,CAACmC,YAAN,KAAuBlC,SAA3B,EAAsC;AAClCD,MAAAA,KAAK,CAACmC,YAAN,GAAqB,GAArB;AACH;;AACD,QAAI,CAACnC,KAAK,CAACoC,KAAX,EAAkB;AACbpC,MAAAA,KAAK,CAACoC,KAAP,GAAyB3B,SAAS,EAAlC;AACH,KAFD,MAEO;AACHW,MAAAA,iBAAiB,CAACpB,KAAK,CAACoC,KAAP,CAAjB;AACH;;AACD,QAAI,CAACpC,KAAK,CAACqC,WAAX,EAAwB;AACnBrC,MAAAA,KAAK,CAACqC,WAAP,GAAqCP,eAAe,EAApD;AACH,KAFD,MAEO;AACHC,MAAAA,uBAAuB,CAAC/B,KAAK,CAACqC,WAAP,CAAvB;AACH;;AACD,QAAI,CAACrC,KAAK,CAACsC,YAAX,EAAyB;AACpBtC,MAAAA,KAAK,CAACsC,YAAP,GAAuCjB,gBAAgB,EAAvD;AACH,KAFD,MAEO;AACHG,MAAAA,wBAAwB,CAACxB,KAAK,CAACsC,YAAP,CAAxB;AACH;;AACD,QAAI,CAACtC,KAAK,CAACuC,GAAX,EAAgB;AACXvC,MAAAA,KAAK,CAACuC,GAAP,GAAqBd,OAAO,EAA5B;AACH,KAFD,MAEO;AACHE,MAAAA,eAAe,CAAC3B,KAAK,CAACuC,GAAP,CAAf;AACH;;AACD,QAAI,CAACvC,KAAK,CAACwC,IAAX,EAAiB;AACZxC,MAAAA,KAAK,CAACwC,IAAP,GAAuBZ,QAAQ,EAA/B;AACH,KAFD,MAEO;AACHC,MAAAA,gBAAgB,CAAC7B,KAAK,CAACwC,IAAP,CAAhB;AACH;AACJ;;;cAlSe9C,Q;sBAOAK,gB;cA4BAG,Q;sBAWAM,gB;eAwCAC,S;uBAcAW,iB;sBAsCAC,gB;8BASAG,wB;aAsBAC,O;qBAQAE,e;cAkBAC,Q;sBAOAC,gB;qBAcAC,e;6BAMAC,uB;0BAkBAC,oB;kCAaAS;;;;;;;;AAvQcC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,G,OAAAA,G;;;;;;AA9BtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AACA;;;;;OAGM;AAAE9C,QAAAA;AAAF,O,GAAkB8C,G;;2BA2EZhC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;;AAIZ+B,MAAAA,MAAM,CAAC/B,SAAD,CAAN", "sourcesContent": ["/*\r\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\r\n\r\n https://www.cocos.com\r\n\r\n Permission is hereby granted, free of charge, to any person obtaining a copy\r\n of this software and associated documentation files (the \"Software\"), to deal\r\n in the Software without restriction, including without limitation the rights to\r\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\r\n of the Software, and to permit persons to whom the Software is furnished to do so,\r\n subject to the following conditions:\r\n\r\n The above copyright notice and this permission notice shall be included in\r\n all copies or substantial portions of the Software.\r\n\r\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\n THE SOFTWARE.\r\n*/\r\n\r\n/**\r\n * ========================= !DO NOT CHANGE THE FOLLOWING SECTION MANUALLY! =========================\r\n * The following section is auto-generated.\r\n * ========================= !DO NOT CHANGE THE FOLLOWING SECTION MANUALLY! =========================\r\n */\r\n/* eslint-disable max-len */\r\nimport { Material, Texture2D, ccenum, gfx } from 'cc';\r\n\r\nconst { SampleCount } = gfx;\r\n\r\nexport interface MSAA {\r\n    enabled: boolean; /* false */\r\n    sampleCount: gfx.SampleCount; /* SampleCount.X4 */\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeMSAA(): MSAA {\r\n    return {\r\n        enabled: false,\r\n        sampleCount: SampleCount.X4,\r\n    };\r\n}\r\n\r\nexport function fillRequiredMSAA(value: MSAA): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.sampleCount === undefined) {\r\n        value.sampleCount = SampleCount.X4;\r\n    }\r\n}\r\n\r\nexport interface ForwardPassConfigs {\r\n    enableMainLightShadowMap: boolean; /* false */\r\n    enableMainLightPlanarShadowMap: boolean; /* false */\r\n    enablePlanarReflectionProbe: boolean; /* false */\r\n    enableMSAA: boolean; /* false */\r\n    enableSingleForwardPass: boolean; /* false */\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport interface HBAO {\r\n    enabled: boolean; /* false */\r\n    radiusScale: number; /* 1 */\r\n    angleBiasDegree: number; /* 10 */\r\n    blurSharpness: number; /* 3 */\r\n    aoSaturation: number; /* 1 */\r\n    needBlur: boolean; /* false */\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeHBAO(): HBAO {\r\n    return {\r\n        enabled: false,\r\n        radiusScale: 1,\r\n        angleBiasDegree: 10,\r\n        blurSharpness: 3,\r\n        aoSaturation: 1,\r\n        needBlur: false,\r\n    };\r\n}\r\n\r\nexport function fillRequiredHBAO(value: HBAO): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.radiusScale === undefined) {\r\n        value.radiusScale = 1;\r\n    }\r\n    if (value.angleBiasDegree === undefined) {\r\n        value.angleBiasDegree = 10;\r\n    }\r\n    if (value.blurSharpness === undefined) {\r\n        value.blurSharpness = 3;\r\n    }\r\n    if (value.aoSaturation === undefined) {\r\n        value.aoSaturation = 1;\r\n    }\r\n    if (value.needBlur === undefined) {\r\n        value.needBlur = false;\r\n    }\r\n}\r\n\r\nexport enum BloomType {\r\n    KawaseDualFilter,\r\n    MipmapFilter,\r\n}\r\nccenum(BloomType);\r\n\r\nexport interface Bloom {\r\n    enabled: boolean; /* false */\r\n    type: BloomType; /* BloomType.KawaseDualFilter */\r\n    /* refcount */ material: Material | null;\r\n    /* refcount */ kawaseFilterMaterial: Material | null;\r\n    /* refcount */ mipmapFilterMaterial: Material | null;\r\n    enableAlphaMask: boolean; /* false */\r\n    iterations: number; /* 3 */\r\n    threshold: number; /* 0.8 */\r\n    intensity: number; /* 1 */\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeBloom(): Bloom {\r\n    return {\r\n        enabled: false,\r\n        type: BloomType.KawaseDualFilter,\r\n        material: null,\r\n        kawaseFilterMaterial: null,\r\n        mipmapFilterMaterial: null,\r\n        enableAlphaMask: false,\r\n        iterations: 3,\r\n        threshold: 0.8,\r\n        intensity: 1,\r\n    };\r\n}\r\n\r\nexport function fillRequiredBloom(value: Bloom): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.type === undefined) {\r\n        value.type = BloomType.KawaseDualFilter;\r\n    }\r\n    if (value.material === undefined) {\r\n        value.material = null;\r\n    }\r\n    if (value.kawaseFilterMaterial === undefined) {\r\n        value.kawaseFilterMaterial = value.material || null;\r\n    }\r\n    if (value.mipmapFilterMaterial === undefined) {\r\n        value.mipmapFilterMaterial = null;\r\n    }\r\n    if (value.enableAlphaMask === undefined) {\r\n        value.enableAlphaMask = false;\r\n    }\r\n    if (value.iterations === undefined) {\r\n        value.iterations = 3;\r\n    }\r\n    if (value.threshold === undefined) {\r\n        value.threshold = 0.8;\r\n    }\r\n    if (value.intensity === undefined) {\r\n        value.intensity = 1;\r\n    }\r\n}\r\n\r\nexport interface ColorGrading {\r\n    enabled: boolean; /* false */\r\n    /* refcount */ material: Material | null;\r\n    contribute: number; /* 1 */\r\n    /* refcount */ colorGradingMap: Texture2D | null;\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeColorGrading(): ColorGrading {\r\n    return {\r\n        enabled: false,\r\n        material: null,\r\n        contribute: 1,\r\n        colorGradingMap: null,\r\n    };\r\n}\r\n\r\nexport function fillRequiredColorGrading(value: ColorGrading): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.material === undefined) {\r\n        value.material = null;\r\n    }\r\n    if (value.contribute === undefined) {\r\n        value.contribute = 1;\r\n    }\r\n    if (value.colorGradingMap === undefined) {\r\n        value.colorGradingMap = null;\r\n    }\r\n}\r\n\r\nexport interface FSR {\r\n    enabled: boolean; /* false */\r\n    /* refcount */ material: Material | null;\r\n    sharpness: number; /* 0.8 */\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeFSR(): FSR {\r\n    return {\r\n        enabled: false,\r\n        material: null,\r\n        sharpness: 0.8,\r\n    };\r\n}\r\n\r\nexport function fillRequiredFSR(value: FSR): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.material === undefined) {\r\n        value.material = null;\r\n    }\r\n    if (value.sharpness === undefined) {\r\n        value.sharpness = 0.8;\r\n    }\r\n}\r\n\r\nexport interface FXAA {\r\n    enabled: boolean; /* false */\r\n    /* refcount */ material: Material | null;\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeFXAA(): FXAA {\r\n    return {\r\n        enabled: false,\r\n        material: null,\r\n    };\r\n}\r\n\r\nexport function fillRequiredFXAA(value: FXAA): void {\r\n    if (value.enabled === undefined) {\r\n        value.enabled = false;\r\n    }\r\n    if (value.material === undefined) {\r\n        value.material = null;\r\n    }\r\n}\r\n\r\nexport interface ToneMapping {\r\n    /* refcount */ material: Material | null;\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makeToneMapping(): ToneMapping {\r\n    return {\r\n        material: null,\r\n    };\r\n}\r\n\r\nexport function fillRequiredToneMapping(value: ToneMapping): void {\r\n    if (value.material === undefined) {\r\n        value.material = null;\r\n    }\r\n}\r\n\r\nexport interface PipelineSettings {\r\n    readonly msaa: MSAA;\r\n    enableShadingScale: boolean; /* false */\r\n    shadingScale: number; /* 0.5 */\r\n    readonly bloom: Bloom;\r\n    readonly toneMapping: ToneMapping;\r\n    readonly colorGrading: ColorGrading;\r\n    readonly fsr: FSR;\r\n    readonly fxaa: FXAA;\r\n    [name: string]: unknown;\r\n}\r\n\r\nexport function makePipelineSettings(): PipelineSettings {\r\n    return {\r\n        msaa: makeMSAA(),\r\n        enableShadingScale: false,\r\n        shadingScale: 0.5,\r\n        bloom: makeBloom(),\r\n        toneMapping: makeToneMapping(),\r\n        colorGrading: makeColorGrading(),\r\n        fsr: makeFSR(),\r\n        fxaa: makeFXAA(),\r\n    };\r\n}\r\n\r\nexport function fillRequiredPipelineSettings(value: PipelineSettings): void {\r\n    if (!value.msaa) {\r\n        (value.msaa as MSAA) = makeMSAA();\r\n    } else {\r\n        fillRequiredMSAA(value.msaa);\r\n    }\r\n    if (value.enableShadingScale === undefined) {\r\n        value.enableShadingScale = false;\r\n    }\r\n    if (value.shadingScale === undefined) {\r\n        value.shadingScale = 0.5;\r\n    }\r\n    if (!value.bloom) {\r\n        (value.bloom as Bloom) = makeBloom();\r\n    } else {\r\n        fillRequiredBloom(value.bloom);\r\n    }\r\n    if (!value.toneMapping) {\r\n        (value.toneMapping as ToneMapping) = makeToneMapping();\r\n    } else {\r\n        fillRequiredToneMapping(value.toneMapping);\r\n    }\r\n    if (!value.colorGrading) {\r\n        (value.colorGrading as ColorGrading) = makeColorGrading();\r\n    } else {\r\n        fillRequiredColorGrading(value.colorGrading);\r\n    }\r\n    if (!value.fsr) {\r\n        (value.fsr as FSR) = makeFSR();\r\n    } else {\r\n        fillRequiredFSR(value.fsr);\r\n    }\r\n    if (!value.fxaa) {\r\n        (value.fxaa as FXAA) = makeFXAA();\r\n    } else {\r\n        fillRequiredFXAA(value.fxaa);\r\n    }\r\n}\r\n"]}