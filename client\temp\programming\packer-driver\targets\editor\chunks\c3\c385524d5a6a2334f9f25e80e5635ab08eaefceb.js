System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, FrameAnimationCmpt, getSoldierFrameAnimConf, GameModel, EventType, DragTouchType, SoldierState, DragTouchCmpt, _class, _crd, ccclass, property, SoldierCmpt;

  function _reportPossibleCrUseOfFrameAnimationCmpt(extras) {
    _reporterNs.report("FrameAnimationCmpt", "../cmpt/FrameAnimationCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfgetSoldierFrameAnimConf(extras) {
    _reporterNs.report("getSoldierFrameAnimConf", "../../common/config/SoldierFrameAnimConf", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameModel(extras) {
    _reporterNs.report("GameModel", "../../model/game/GameModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventType(extras) {
    _reporterNs.report("EventType", "../../common/event/EventType", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttrBarCmpt(extras) {
    _reporterNs.report("AttrBarCmpt", "./AttrBarCmpt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSoldierObj(extras) {
    _reporterNs.report("SoldierObj", "../../model/game/SoldierObj", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDragTouchType(extras) {
    _reporterNs.report("DragTouchType", "../../common/constant/Enums", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSoldierState(extras) {
    _reporterNs.report("SoldierState", "../../common/constant/Enums", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDragTouchCmpt(extras) {
    _reporterNs.report("DragTouchCmpt", "./DragTouchCmpt", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      FrameAnimationCmpt = _unresolved_2.default;
    }, function (_unresolved_3) {
      getSoldierFrameAnimConf = _unresolved_3.getSoldierFrameAnimConf;
    }, function (_unresolved_4) {
      GameModel = _unresolved_4.default;
    }, function (_unresolved_5) {
      EventType = _unresolved_5.default;
    }, function (_unresolved_6) {
      DragTouchType = _unresolved_6.DragTouchType;
      SoldierState = _unresolved_6.SoldierState;
    }, function (_unresolved_7) {
      DragTouchCmpt = _unresolved_7.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0a0e3MOAtZDWZIDwFjCf7M8", "SoldierCmpt", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'Node', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator); // 一个动物

      _export("default", SoldierCmpt = ccclass(_class = class SoldierCmpt extends Component {
        constructor(...args) {
          super(...args);
          this.key = '';
          this.data = null;
          this.body = null;
          this.animNode = null;
          this.animCmpt = null;
          this.touchCmpt = null;
          this.currAnimName = '';
          this.attrBar = null;
          this.preStateUid = '';
        }

        init(data, pos, key) {
          this.data = data;
          this.key = key;
          this.node.setPosition(pos);
          this.body = this.FindChild('body'); // this.body.scale.set(this.data.isFriendly() ? 1 : -1, 1)

          this.animNode = this.FindChild('body/anim');
          this.animCmpt = this.animNode.getComponent(_crd && FrameAnimationCmpt === void 0 ? (_reportPossibleCrUseOfFrameAnimationCmpt({
            error: Error()
          }), FrameAnimationCmpt) : FrameAnimationCmpt);
          this.animCmpt.setUpdateModel((_crd && GameModel === void 0 ? (_reportPossibleCrUseOfGameModel({
            error: Error()
          }), GameModel) : GameModel).ins());
          this.animCmpt.init((_crd && getSoldierFrameAnimConf === void 0 ? (_reportPossibleCrUseOfgetSoldierFrameAnimConf({
            error: Error()
          }), getSoldierFrameAnimConf) : getSoldierFrameAnimConf)(data.getViewId()), key).then(() => this.isValid && this.playAnimation('idle'));
          this.touchCmpt = this.FindChild('touch').addComponent(_crd && DragTouchCmpt === void 0 ? (_reportPossibleCrUseOfDragTouchCmpt({
            error: Error()
          }), DragTouchCmpt) : DragTouchCmpt).init(this);
          this.loadAttrBar();
          return this;
        }

        resync(data) {
          return this;
        }

        clean(release) {
          var _this$data;

          this.animCmpt.clean();
          this.node.destroy();
          release && assetsMgr.releaseTempRes((_this$data = this.data) == null ? void 0 : _this$data.getPrefabUrl(), this.key);
          this.data = null;
        }

        async loadAttrBar() {// const it = await nodePoolMgr.get('animal/ATTR_BAR', this.key)
          // if (!this.isValid || !this.data) {
          //     return nodePoolMgr.put(it)
          // }
          // it.parent = this.node
          // it.zIndex = 10
          // it.active = true
          // this.attrBar = it.getComponent(AttrBarCmpt).init(this.data)
        }

        get uid() {
          var _this$data2;

          return ((_this$data2 = this.data) == null ? void 0 : _this$data2.uid) || '';
        }

        getBody() {
          return this.body;
        } // 触摸事件


        onTouchEvent(type, event) {
          console.log('onTouchEvent', (_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
            error: Error()
          }), DragTouchType) : DragTouchType)[type], event);

          if (type === (_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
            error: Error()
          }), DragTouchType) : DragTouchType).DRAG_BEGIN) {
            this.node.y += 40;
          } else if (type === (_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
            error: Error()
          }), DragTouchType) : DragTouchType).DRAG_MOVE) {
            const pos = this.node.parent.transform.convertToNodeSpaceAR(event.getUILocation().toVec3());
            console.log(pos.toString());
          } else if (type = (_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
            error: Error()
          }), DragTouchType) : DragTouchType).CLICK) {} else if (type = (_crd && DragTouchType === void 0 ? (_reportPossibleCrUseOfDragTouchType({
            error: Error()
          }), DragTouchType) : DragTouchType).END) {
            this.node.y -= 40;
          }
        }

        update(dt) {
          if (!this.data) {
            return;
          }

          this.updateState();
        } // 播放动画


        playAnimation(name, cb, startTime) {
          var _this$animCmpt;

          this.currAnimName = name;
          (_this$animCmpt = this.animCmpt) == null || _this$animCmpt.play(name, cb, startTime);
        } // 同步状态信息


        updateState() {
          var _this$data3;

          if (!((_this$data3 = this.data) != null && _this$data3.state) || this.preStateUid === this.data.state.uid) {
            return;
          }

          this.preStateUid = this.data.state.uid;
          const state = this.data.state.type,
                data = this.data.state.data; // cc.log('updateState', this.uid, this.point.ID(), SoldierState[state])
          // this.data.actioning = this.data.actioning || (state !== SoldierState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动

          if (state === (_crd && SoldierState === void 0 ? (_reportPossibleCrUseOfSoldierState({
            error: Error()
          }), SoldierState) : SoldierState).STAND) {
            //待机
            this.doStand();
          } else if (state === (_crd && SoldierState === void 0 ? (_reportPossibleCrUseOfSoldierState({
            error: Error()
          }), SoldierState) : SoldierState).ATTACK) {
            //攻击
            this.doAttack(data);
          } else if (state === (_crd && SoldierState === void 0 ? (_reportPossibleCrUseOfSoldierState({
            error: Error()
          }), SoldierState) : SoldierState).HIT) {
            //受击
            this.doHit(data);
          } else {
            this.playAnimation('idle');
          }
        } // 待机


        doStand() {
          var _this$animCmpt2, _this$attrBar;

          const animName = (_this$animCmpt2 = this.animCmpt) == null ? void 0 : _this$animCmpt2.playAnimName;

          if (animName === 'move' || animName === 'move_pull') {
            //只有移动的时候才强行切换成idle
            this.playAnimation('idle');
          }

          (_this$attrBar = this.attrBar) == null || _this$attrBar.reset();
        } // 攻击


        doAttack(data) {
          var _data$currAttackTime;

          const currAttackTime = (_data$currAttackTime = data.currAttackTime) != null ? _data$currAttackTime : 0;
          const suffix = data.instabilityAttackIndex || '';
          this.playAnimation('attack' + suffix, () => this.isValid && this.playAnimation('idle'), currAttackTime);
        } // 受击


        doHit(data) {
          var _data$damage, _this$attrBar2;

          let damage = (_data$damage = data.damage) != null ? _data$damage : 0;
          const isDie = this.data.isDie();
          const sound = data.sound; //受击音效

          const uid = this.uid;
          (_this$attrBar2 = this.attrBar) == null || _this$attrBar2.play();

          if (damage === 0) {
            return this.playAnimation('idle');
          }

          let animName = 'hit';

          if (isDie) {
            animName = 'die'; // this.playSFXByKey('die_sound')
          } else if (sound) {// this.playSFX(sound)
          }

          this.playAnimation(animName, () => {
            if (isDie) {
              eventCenter.emit((_crd && EventType === void 0 ? (_reportPossibleCrUseOfEventType({
                error: Error()
              }), EventType) : EventType).REMOVE_ANIMAL, uid, false);
            } else if (this.isValid) {
              this.playAnimation('idle');
            }
          });
        } // 直接死亡


        doDie(data) {}

      }) || _class);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c385524d5a6a2334f9f25e80e5635ab08eaefceb.js.map