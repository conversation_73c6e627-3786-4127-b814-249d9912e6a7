{"version": 3, "sources": ["file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts"], "names": ["Color", "<PERSON><PERSON>", "UITransform", "instantiate", "Toggle", "_decorator", "Component", "<PERSON><PERSON>", "director", "Node", "Label", "RichText", "ccclass", "property", "DebugViewRuntimeControl", "_single", "str<PERSON><PERSON>le", "strComposite", "strMisc", "compositeModeToggleList", "singleModeToggleList", "miscModeToggleList", "textComponentList", "labelComponentList", "textContentList", "hideButtonLabel", "_currentColorIndex", "strColor", "color", "WHITE", "BLACK", "RED", "GREEN", "BLUE", "start", "canvas", "node", "parent", "getComponent", "console", "error", "uiTransform", "halfScreenWidth", "width", "halfScreenHeight", "height", "x", "y", "miscNode", "getChildByName", "buttonNode", "name", "titleNode", "i", "new<PERSON>abel", "EnableAllCompositeModeButton", "setPosition", "setScale", "labelComponent", "string", "overflow", "length", "currentRow", "newNode", "singleModeToggle", "textComponent", "getComponentInChildren", "on", "EventType", "TOGGLE", "toggleSingleMode", "CLICK", "enableAllCompositeMode", "changeColorButton", "changeTextColor", "HideButton", "hideUI", "compositeModeToggle", "toggleComponent", "isChecked", "toggleLightingWithAlbedo", "toggleCSMColoration", "toggleCompositeMode", "isTextMatched", "textUI", "textDescription", "tempText", "String", "findIndex", "search", "substr", "toggle", "debugView", "root", "singleMode", "enableCompositeMode", "lightingWithAlbedo", "csmLayerColoration", "button", "activeValue", "active", "onLoad", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,M,OAAAA,M;AAAqBC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAA8BC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;AAAwCC,MAAAA,K,OAAAA,K;AAAwBC,MAAAA,Q,OAAAA,Q;;;;;;;;;OACtM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yCAGjBS,uB,WADZF,OAAO,CAAC,kCAAD,C,UAEHC,QAAQ,CAACJ,IAAD,C,UAERI,QAAQ,CAACJ,IAAD,C,UAERI,QAAQ,CAACJ,IAAD,C,2BANb,MACaK,uBADb,SAC6CR,SAD7C,CACuD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAOtDS,OAPsD,GAOpC,CAPoC;AAAA,eAS3CC,SAT2C,GASrB,CAC1B,iBAD0B,EAE1B,cAF0B,EAG1B,eAH0B,EAI1B,gBAJ0B,EAK1B,gBAL0B,EAM1B,eAN0B,EAO1B,WAP0B,EAQ1B,KAR0B,EAS1B,KAT0B,EAU1B,aAV0B,EAW1B,eAX0B,EAY1B,cAZ0B,EAc1B,iBAd0B,EAe1B,kBAf0B,EAgB1B,mBAhB0B,EAiB1B,YAjB0B,EAkB1B,eAlB0B,EAmB1B,gBAnB0B,EAoB1B,cApB0B,EAqB1B,UArB0B,EAsB1B,WAtB0B,EAuB1B,oBAvB0B,EAwB1B,KAxB0B,EA0B1B,gBA1B0B,EA2B1B,iBA3B0B,EA4B1B,YA5B0B,EA6B1B,aA7B0B,EA8B1B,cA9B0B,EA+B1B,SA/B0B,EAgC1B,UAhC0B,EAiC1B,WAjC0B,EAkC1B,QAlC0B,EAmC1B,IAnC0B,EAqC1B,SArC0B,EAsC1B,yBAtC0B,EAuC1B,0BAvC0B,EAwC1B,sBAxC0B,EAyC1B,uBAzC0B,EA0C1B,cA1C0B,EA2C1B,0BA3C0B,EA4C1B,uBA5C0B,EA6C1B,cA7C0B,EA+C1B,KA/C0B,CATqB;AAAA,eA0D3CC,YA1D2C,GA0DlB,CAC7B,gBAD6B,EAE7B,iBAF6B,EAG7B,aAH6B,EAI7B,cAJ6B,EAK7B,UAL6B,EAM7B,WAN6B,EAO7B,QAP6B,EAQ7B,IAR6B,EAU7B,YAV6B,EAW7B,KAX6B,EAa7B,cAb6B,EAc7B,kBAd6B,EAgB7B,SAhB6B,EAiB7B,kBAjB6B,EAkB7B,mBAlB6B,EAmB7B,mBAnB6B,EAoB7B,IApB6B,CA1DkB;AAAA,eAgF3CC,OAhF2C,GAgFvB,CACxB,sBADwB,EAExB,sBAFwB,CAhFuB;AAAA,eAqF3CC,uBArF2C,GAqFT,EArFS;AAAA,eAsF3CC,oBAtF2C,GAsFZ,EAtFY;AAAA,eAuF3CC,kBAvF2C,GAuFd,EAvFc;AAAA,eAwF3CC,iBAxF2C,GAwFX,EAxFW;AAAA,eAyF3CC,kBAzF2C,GAyFb,EAzFa;AAAA,eA0F3CC,eA1F2C,GA0Ff,EA1Fe;AAAA,eA2F3CC,eA3F2C;AAAA,eAyR3CC,kBAzR2C,GAyRtB,CAzRsB;AAAA,eA0R3CC,QA1R2C,GA0RtB,CACzB,iBADyB,EAEzB,iBAFyB,EAGzB,iBAHyB,EAIzB,iBAJyB,EAKzB,iBALyB,CA1RsB;AAAA,eAiS3CC,KAjS2C,GAiS1B,CACrB5B,KAAK,CAAC6B,KADe,EAErB7B,KAAK,CAAC8B,KAFe,EAGrB9B,KAAK,CAAC+B,GAHe,EAIrB/B,KAAK,CAACgC,KAJe,EAKrBhC,KAAK,CAACiC,IALe,CAjS0B;AAAA;;AA4FnDC,QAAAA,KAAK,GAAG;AACJ;AACA,gBAAMC,MAAM,GAAG,KAAKC,IAAL,CAAUC,MAAV,CAAiBC,YAAjB,CAA8BrC,MAA9B,CAAf;;AACA,cAAI,CAACkC,MAAL,EAAa;AACTI,YAAAA,OAAO,CAACC,KAAR,CAAc,sDAAd;AACA;AACH;;AAED,gBAAMC,WAAW,GAAG,KAAKL,IAAL,CAAUC,MAAV,CAAiBC,YAAjB,CAA8BpC,WAA9B,CAApB;AACA,gBAAMwC,eAAe,GAAGD,WAAW,CAACE,KAAZ,GAAoB,GAA5C;AACA,gBAAMC,gBAAgB,GAAGH,WAAW,CAACI,MAAZ,GAAqB,GAA9C;AAEA,cAAIC,CAAC,GAAG,CAACJ,eAAD,GAAmBA,eAAe,GAAG,GAA7C;AAAA,cAAkDK,CAAC,GAAGH,gBAAgB,GAAGA,gBAAgB,GAAG,GAA5F;AACA,gBAAMD,KAAK,GAAG,GAAd;AAAA,gBAAmBE,MAAM,GAAG,EAA5B,CAbI,CAeJ;;AACA,gBAAMG,QAAQ,GAAG,KAAKZ,IAAL,CAAUa,cAAV,CAAyB,UAAzB,CAAjB;AACA,gBAAMC,UAAU,GAAG/C,WAAW,CAAC6C,QAAD,CAA9B;AACAE,UAAAA,UAAU,CAACb,MAAX,GAAoB,KAAKD,IAAzB;AACAc,UAAAA,UAAU,CAACC,IAAX,GAAkB,SAAlB;AACA,gBAAMC,SAAS,GAAGjD,WAAW,CAAC6C,QAAD,CAA7B;AACAI,UAAAA,SAAS,CAACf,MAAV,GAAmB,KAAKD,IAAxB;AACAgB,UAAAA,SAAS,CAACD,IAAV,GAAiB,QAAjB,CAtBI,CAwBJ;;AACA,eAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AACxB,kBAAMC,QAAQ,GAAGnD,WAAW,CAAC,KAAKoD,4BAAL,CAAkCN,cAAlC,CAAiD,OAAjD,CAAD,CAA5B;AACAK,YAAAA,QAAQ,CAACE,WAAT,CAAqBV,CAAC,IAAIO,CAAC,GAAG,CAAJ,GAAQ,KAAKV,KAAK,GAAG,CAArB,GAAyB,GAA7B,CAAtB,EAAyDI,CAAzD,EAA4D,GAA5D;AACAO,YAAAA,QAAQ,CAACG,QAAT,CAAkB,IAAlB,EAAwB,IAAxB,EAA8B,IAA9B;AACAH,YAAAA,QAAQ,CAACjB,MAAT,GAAkBe,SAAlB;AACA,kBAAMM,cAAc,GAAGJ,QAAQ,CAAChB,YAAT,CAAsB5B,KAAtB,CAAvB;AACAgD,YAAAA,cAAc,CAACC,MAAf,GAAwBN,CAAC,GAAG,oCAAH,GAA0C,iCAAnE;AACAK,YAAAA,cAAc,CAAC9B,KAAf,GAAuB5B,KAAK,CAAC6B,KAA7B;AACA6B,YAAAA,cAAc,CAACE,QAAf,GAA0B,CAA1B;AACA,iBAAKrC,kBAAL,CAAwB,KAAKA,kBAAL,CAAwBsC,MAAhD,IAA0DH,cAA1D;AACH;;AAEDX,UAAAA,CAAC,IAAIF,MAAL,CArCI,CAsCJ;;AACA,cAAIiB,UAAU,GAAG,CAAjB;;AACA,eAAK,IAAIT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrC,SAAL,CAAe6C,MAAnC,EAA2CR,CAAC,IAAIS,UAAU,EAA1D,EAA8D;AAC1D,gBAAIT,CAAC,KAAK,KAAKrC,SAAL,CAAe6C,MAAf,IAAyB,CAAnC,EAAsC;AAClCf,cAAAA,CAAC,IAAIH,KAAL;AACAmB,cAAAA,UAAU,GAAG,CAAb;AACH;;AACD,kBAAMC,OAAO,GAAGV,CAAC,GAAGlD,WAAW,CAAC,KAAK6D,gBAAN,CAAd,GAAwC,KAAKA,gBAA9D;AACAD,YAAAA,OAAO,CAACP,WAAR,CAAoBV,CAApB,EAAuBC,CAAC,GAAGF,MAAM,GAAGiB,UAApC,EAAgD,GAAhD;AACAC,YAAAA,OAAO,CAACN,QAAR,CAAiB,GAAjB,EAAsB,GAAtB,EAA2B,GAA3B;AACAM,YAAAA,OAAO,CAAC1B,MAAR,GAAiB,KAAK2B,gBAAL,CAAsB3B,MAAvC;AAEA,kBAAM4B,aAAa,GAAGF,OAAO,CAACG,sBAAR,CAA+BvD,QAA/B,CAAtB;AACAsD,YAAAA,aAAa,CAACN,MAAd,GAAuB,KAAK3C,SAAL,CAAeqC,CAAf,CAAvB;AACA,iBAAK/B,iBAAL,CAAuB,KAAKA,iBAAL,CAAuBuC,MAA9C,IAAwDI,aAAxD;AACA,iBAAKzC,eAAL,CAAqB,KAAKA,eAAL,CAAqBqC,MAA1C,IAAoDI,aAAa,CAACN,MAAlE;AAEAI,YAAAA,OAAO,CAACI,EAAR,CAAW/D,MAAM,CAACgE,SAAP,CAAiBC,MAA5B,EAAoC,KAAKC,gBAAzC,EAA2D,IAA3D;AAEA,iBAAKlD,oBAAL,CAA0BiC,CAA1B,IAA+BU,OAA/B;AACH;;AAEDjB,UAAAA,CAAC,IAAIH,KAAL,CA5DI,CA6DJ;;AACA,eAAKY,4BAAL,CAAkCC,WAAlC,CAA8CV,CAAC,GAAG,EAAlD,EAAsDC,CAAtD,EAAyD,GAAzD;AACA,eAAKQ,4BAAL,CAAkCE,QAAlC,CAA2C,GAA3C,EAAgD,GAAhD,EAAqD,GAArD;AACA,eAAKF,4BAAL,CAAkCY,EAAlC,CAAqC5D,MAAM,CAAC6D,SAAP,CAAiBG,KAAtD,EAA6D,KAAKC,sBAAlE,EAA0F,IAA1F;AACA,eAAKjB,4BAAL,CAAkClB,MAAlC,GAA2Ca,UAA3C;AACA,cAAIQ,cAAc,GAAG,KAAKH,4BAAL,CAAkCW,sBAAlC,CAAyDxD,KAAzD,CAArB;AACA,eAAKa,kBAAL,CAAwB,KAAKA,kBAAL,CAAwBsC,MAAhD,IAA0DH,cAA1D;AAEA,gBAAMe,iBAAiB,GAAGtE,WAAW,CAAC,KAAKoD,4BAAN,CAArC;AACAkB,UAAAA,iBAAiB,CAACjB,WAAlB,CAA8BV,CAAC,GAAG,EAAlC,EAAsCC,CAAtC,EAAyC,GAAzC;AACA0B,UAAAA,iBAAiB,CAAChB,QAAlB,CAA2B,GAA3B,EAAgC,GAAhC,EAAqC,GAArC;AACAgB,UAAAA,iBAAiB,CAACN,EAAlB,CAAqB5D,MAAM,CAAC6D,SAAP,CAAiBG,KAAtC,EAA6C,KAAKG,eAAlD,EAAmE,IAAnE;AACAD,UAAAA,iBAAiB,CAACpC,MAAlB,GAA2Ba,UAA3B;AACAQ,UAAAA,cAAc,GAAGe,iBAAiB,CAACP,sBAAlB,CAAyCxD,KAAzC,CAAjB;AACAgD,UAAAA,cAAc,CAACC,MAAf,GAAwB,WAAxB;AACA,eAAKpC,kBAAL,CAAwB,KAAKA,kBAAL,CAAwBsC,MAAhD,IAA0DH,cAA1D;AAEA,gBAAMiB,UAAU,GAAGxE,WAAW,CAAC,KAAKoD,4BAAN,CAA9B;AACAoB,UAAAA,UAAU,CAACnB,WAAX,CAAuBV,CAAC,GAAG,GAA3B,EAAgCC,CAAhC,EAAmC,GAAnC;AACA4B,UAAAA,UAAU,CAAClB,QAAX,CAAoB,GAApB,EAAyB,GAAzB,EAA8B,GAA9B;AACAkB,UAAAA,UAAU,CAACR,EAAX,CAAc5D,MAAM,CAAC6D,SAAP,CAAiBG,KAA/B,EAAsC,KAAKK,MAA3C,EAAmD,IAAnD;AACAD,UAAAA,UAAU,CAACtC,MAAX,GAAoB,KAAKD,IAAL,CAAUC,MAA9B;AACAqB,UAAAA,cAAc,GAAGiB,UAAU,CAACT,sBAAX,CAAkCxD,KAAlC,CAAjB;AACAgD,UAAAA,cAAc,CAACC,MAAf,GAAwB,SAAxB;AACA,eAAKpC,kBAAL,CAAwB,KAAKA,kBAAL,CAAwBsC,MAAhD,IAA0DH,cAA1D;AACA,eAAKjC,eAAL,GAAuBiC,cAAvB,CAtFI,CAwFJ;;AACAX,UAAAA,CAAC,IAAI,EAAL;;AACA,eAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKnC,OAAL,CAAa2C,MAAjC,EAAyCR,CAAC,EAA1C,EAA8C;AAC1C,kBAAMU,OAAO,GAAG5D,WAAW,CAAC,KAAK0E,mBAAN,CAA3B;AACAd,YAAAA,OAAO,CAACP,WAAR,CAAoBV,CAApB,EAAuBC,CAAC,GAAGF,MAAM,GAAGQ,CAApC,EAAuC,GAAvC;AACAU,YAAAA,OAAO,CAACN,QAAR,CAAiB,GAAjB,EAAsB,GAAtB,EAA2B,GAA3B;AACAM,YAAAA,OAAO,CAAC1B,MAAR,GAAiBW,QAAjB;AAEA,kBAAMiB,aAAa,GAAGF,OAAO,CAACG,sBAAR,CAA+BvD,QAA/B,CAAtB;AACAsD,YAAAA,aAAa,CAACN,MAAd,GAAuB,KAAKzC,OAAL,CAAamC,CAAb,CAAvB;AACA,iBAAK/B,iBAAL,CAAuB,KAAKA,iBAAL,CAAuBuC,MAA9C,IAAwDI,aAAxD;AACA,iBAAKzC,eAAL,CAAqB,KAAKA,eAAL,CAAqBqC,MAA1C,IAAoDI,aAAa,CAACN,MAAlE;AAEA,kBAAMmB,eAAe,GAAGf,OAAO,CAACzB,YAAR,CAAqBlC,MAArB,CAAxB;AACA0E,YAAAA,eAAe,CAACC,SAAhB,GAA4B1B,CAAC,GAAG,IAAH,GAAU,KAAvC;AACAU,YAAAA,OAAO,CAACI,EAAR,CAAW/D,MAAM,CAACgE,SAAP,CAAiBC,MAA5B,EAAoChB,CAAC,GAAG,KAAK2B,wBAAR,GAAmC,KAAKC,mBAA7E,EAAkG,IAAlG;AACA,iBAAK5D,kBAAL,CAAwBgC,CAAxB,IAA6BU,OAA7B;AACH,WAzGG,CA2GJ;;;AACAhB,UAAAA,CAAC,IAAI,GAAL;;AACA,eAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpC,YAAL,CAAkB4C,MAAtC,EAA8CR,CAAC,EAA/C,EAAmD;AAC/C,kBAAMU,OAAO,GAAGV,CAAC,GAAGlD,WAAW,CAAC,KAAK0E,mBAAN,CAAd,GAA2C,KAAKA,mBAAjE;AACAd,YAAAA,OAAO,CAACP,WAAR,CAAoBV,CAApB,EAAuBC,CAAC,GAAGF,MAAM,GAAGQ,CAApC,EAAuC,GAAvC;AACAU,YAAAA,OAAO,CAACN,QAAR,CAAiB,GAAjB,EAAsB,GAAtB,EAA2B,GAA3B;AACAM,YAAAA,OAAO,CAAC1B,MAAR,GAAiB,KAAKwC,mBAAL,CAAyBxC,MAA1C;AAEA,kBAAM4B,aAAa,GAAGF,OAAO,CAACG,sBAAR,CAA+BvD,QAA/B,CAAtB;AACAsD,YAAAA,aAAa,CAACN,MAAd,GAAuB,KAAK1C,YAAL,CAAkBoC,CAAlB,CAAvB;AACA,iBAAK/B,iBAAL,CAAuB,KAAKA,iBAAL,CAAuBuC,MAA9C,IAAwDI,aAAxD;AACA,iBAAKzC,eAAL,CAAqB,KAAKA,eAAL,CAAqBqC,MAA1C,IAAoDI,aAAa,CAACN,MAAlE;AAEAI,YAAAA,OAAO,CAACI,EAAR,CAAW/D,MAAM,CAACgE,SAAP,CAAiBC,MAA5B,EAAoC,KAAKa,mBAAzC,EAA8D,IAA9D;AAEA,iBAAK/D,uBAAL,CAA6BkC,CAA7B,IAAkCU,OAAlC;AACH;AACJ;;AAEDoB,QAAAA,aAAa,CAACC,MAAD,EAASC,eAAT,EAAoC;AAC7C,cAAIC,QAAQ,GAAG,IAAIC,MAAJ,CAAWH,MAAX,CAAf;AACA,gBAAMI,SAAS,GAAGF,QAAQ,CAACG,MAAT,CAAgB,GAAhB,CAAlB;;AACA,cAAID,SAAS,KAAK,CAAC,CAAnB,EAAsB;AAClB,mBAAOJ,MAAM,KAAKC,eAAlB;AACH,WAFD,MAEO;AACHC,YAAAA,QAAQ,GAAGA,QAAQ,CAACI,MAAT,CAAgBF,SAAS,GAAG,CAA5B,CAAX;AACAF,YAAAA,QAAQ,GAAGA,QAAQ,CAACI,MAAT,CAAgB,CAAhB,EAAmBJ,QAAQ,CAACG,MAAT,CAAgB,GAAhB,CAAnB,CAAX;AACA,mBAAOH,QAAQ,KAAKD,eAApB;AACH;AACJ;;AACDf,QAAAA,gBAAgB,CAACqB,MAAD,EAAiB;AAC7B,gBAAMC,SAAS,GAAGpF,QAAQ,CAACqF,IAAT,CAAeD,SAAjC;AACA,gBAAM3B,aAAa,GAAG0B,MAAM,CAACzB,sBAAP,CAA8BvD,QAA9B,CAAtB;;AACA,eAAK,IAAI0C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrC,SAAL,CAAe6C,MAAnC,EAA2CR,CAAC,EAA5C,EAAgD;AAC5C,gBAAI,KAAK8B,aAAL,CAAmBlB,aAAa,CAACN,MAAjC,EAAyC,KAAK3C,SAAL,CAAeqC,CAAf,CAAzC,CAAJ,EAAiE;AAC7DuC,cAAAA,SAAS,CAACE,UAAV,GAAuBzC,CAAvB;AACH;AACJ;AACJ;;AACD6B,QAAAA,mBAAmB,CAACS,MAAD,EAAiB;AAChC,gBAAMC,SAAS,GAAGpF,QAAQ,CAACqF,IAAT,CAAeD,SAAjC;AACA,gBAAM3B,aAAa,GAAG0B,MAAM,CAACzB,sBAAP,CAA8BvD,QAA9B,CAAtB;;AACA,eAAK,IAAI0C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpC,YAAL,CAAkB4C,MAAtC,EAA8CR,CAAC,EAA/C,EAAmD;AAC/C,gBAAI,KAAK8B,aAAL,CAAmBlB,aAAa,CAACN,MAAjC,EAAyC,KAAK1C,YAAL,CAAkBoC,CAAlB,CAAzC,CAAJ,EAAoE;AAChEuC,cAAAA,SAAS,CAACG,mBAAV,CAA8B1C,CAA9B,EAAiCsC,MAAM,CAACZ,SAAxC;AACH;AACJ;AACJ;;AACDC,QAAAA,wBAAwB,CAACW,MAAD,EAAiB;AACrC,gBAAMC,SAAS,GAAGpF,QAAQ,CAACqF,IAAT,CAAeD,SAAjC;AACAA,UAAAA,SAAS,CAACI,kBAAV,GAA+BL,MAAM,CAACZ,SAAtC;AACH;;AACDE,QAAAA,mBAAmB,CAACU,MAAD,EAAiB;AAChC,gBAAMC,SAAS,GAAGpF,QAAQ,CAACqF,IAAT,CAAeD,SAAjC;AACAA,UAAAA,SAAS,CAACK,kBAAV,GAA+BN,MAAM,CAACZ,SAAtC;AACH;;AACDP,QAAAA,sBAAsB,CAAC0B,MAAD,EAAiB;AACnC,gBAAMN,SAAS,GAAGpF,QAAQ,CAACqF,IAAT,CAAeD,SAAjC;AACAA,UAAAA,SAAS,CAACpB,sBAAV,CAAiC,IAAjC;;AACA,eAAK,IAAInB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlC,uBAAL,CAA6B0C,MAAjD,EAAyDR,CAAC,EAA1D,EAA8D;AAC1D,kBAAMyB,eAAe,GAAG,KAAK3D,uBAAL,CAA6BkC,CAA7B,EAAgCf,YAAhC,CAA6ClC,MAA7C,CAAxB;AACA0E,YAAAA,eAAe,CAACC,SAAhB,GAA4B,IAA5B;AACH;;AAED,cAAID,eAAe,GAAG,KAAKzD,kBAAL,CAAwB,CAAxB,EAA2BiB,YAA3B,CAAwClC,MAAxC,CAAtB;AACA0E,UAAAA,eAAe,CAACC,SAAhB,GAA4B,KAA5B;AACAa,UAAAA,SAAS,CAACK,kBAAV,GAA+B,KAA/B;AACAnB,UAAAA,eAAe,GAAG,KAAKzD,kBAAL,CAAwB,CAAxB,EAA2BiB,YAA3B,CAAwClC,MAAxC,CAAlB;AACA0E,UAAAA,eAAe,CAACC,SAAhB,GAA4B,IAA5B;AACAa,UAAAA,SAAS,CAACI,kBAAV,GAA+B,IAA/B;AACH;;AACDpB,QAAAA,MAAM,CAACsB,MAAD,EAAiB;AACnB,gBAAM9C,SAAS,GAAG,KAAKhB,IAAL,CAAUa,cAAV,CAAyB,QAAzB,CAAlB;AACA,gBAAMkD,WAAW,GAAG,CAAC/C,SAAS,CAACgD,MAA/B;AACA,eAAKhF,oBAAL,CAA0B,CAA1B,EAA6BiB,MAA7B,CAAoC+D,MAApC,GAA6CD,WAA7C;AACA,eAAK9E,kBAAL,CAAwB,CAAxB,EAA2BgB,MAA3B,CAAkC+D,MAAlC,GAA2CD,WAA3C;AACA,eAAKhF,uBAAL,CAA6B,CAA7B,EAAgCkB,MAAhC,CAAuC+D,MAAvC,GAAgDD,WAAhD;AACA,eAAK5C,4BAAL,CAAkClB,MAAlC,CAAyC+D,MAAzC,GAAkDD,WAAlD;AACA/C,UAAAA,SAAS,CAACgD,MAAV,GAAmBD,WAAnB;AACA,eAAK1E,eAAL,CAAqBkC,MAArB,GAA8BwC,WAAW,GAAG,SAAH,GAAe,SAAxD;AACH;;AAiBDzB,QAAAA,eAAe,CAACwB,MAAD,EAAiB;AAC5B,eAAKxE,kBAAL;;AACA,cAAI,KAAKA,kBAAL,IAA2B,KAAKC,QAAL,CAAckC,MAA7C,EAAqD;AACjD,iBAAKnC,kBAAL,GAA0B,CAA1B;AACH;;AACD,eAAK,IAAI2B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/B,iBAAL,CAAuBuC,MAA3C,EAAmDR,CAAC,EAApD,EAAwD;AACpD,iBAAK/B,iBAAL,CAAuB+B,CAAvB,EAA0BM,MAA1B,GAAmC,KAAKhC,QAAL,CAAc,KAAKD,kBAAnB,IAAyC,KAAKF,eAAL,CAAqB6B,CAArB,CAAzC,GAAmE,UAAtG;AACH;;AACD,eAAK,IAAIA,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK9B,kBAAL,CAAwBsC,MAA5C,EAAoDR,CAAC,EAArD,EAAyD;AACrD,iBAAK9B,kBAAL,CAAwB8B,CAAxB,EAA2BzB,KAA3B,GAAmC,KAAKA,KAAL,CAAW,KAAKF,kBAAhB,CAAnC;AACH;AACJ;;AAED2E,QAAAA,MAAM,GAAG,CACR;;AACDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CACzB;;AAxTkD,O;;;;;iBAEhB,I;;;;;;;iBAEH,I;;;;;;;iBAEY,I", "sourcesContent": ["import { Color, Canvas, UITransform, instantiate, math, Toggle, TextureCube, _decorator, Component, But<PERSON>, label<PERSON><PERSON>mbler, game, director, Node, Scene, renderer, CameraComponent, Label, ForwardPipeline, RichText } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('internal.DebugViewRuntimeControl')\r\nexport class DebugViewRuntimeControl extends Component {\r\n    @property(Node)\r\n    compositeModeToggle: Node | null = null;\r\n    @property(Node)\r\n    singleModeToggle: Node | null = null;\r\n    @property(Node)\r\n    EnableAllCompositeModeButton: Node | null = null;\r\n\t_single: number = 0;\r\n\r\n    private strSingle: string[] = [\r\n        'No Single Debug',\r\n        'Vertex Color',\r\n        'Vertex Normal',\r\n        'Vertex Tangent',\r\n        'World Position',\r\n        'Vertex Mirror',\r\n        'Face Side',\r\n        'UV0',\r\n        'UV1',\r\n        'UV Lightmap',\r\n        'Project Depth',\r\n        'Linear Depth',\r\n\r\n        'Fragment Normal',\r\n        'Fragment Tangent',\r\n        'Fragment Binormal',\r\n        'Base Color',\r\n        'Diffuse Color',\r\n        'Specular Color',\r\n        'Transparency',\r\n        'Metallic',\r\n        'Roughness',\r\n        'Specular Intensity',\r\n        'IOR',\r\n\r\n        'Direct Diffuse',\r\n        'Direct Specular',\r\n        'Direct All',\r\n        'Env Diffuse',\r\n        'Env Specular',\r\n        'Env All',\r\n        'Emissive',\r\n        'Light Map',\r\n        'Shadow',\r\n        'AO',\r\n\r\n        'Fresnel',\r\n        'Direct Transmit Diffuse',\r\n        'Direct Transmit Specular',\r\n        'Env Transmit Diffuse',\r\n        'Env Transmit Specular',\r\n        'Transmit All',\r\n        'Direct Internal Specular',\r\n        'Env Internal Specular',\r\n        'Internal All',\r\n\r\n        'Fog',\r\n    ];\r\n    private strComposite: string[] = [\r\n        'Direct Diffuse',\r\n        'Direct Specular',\r\n        'Env Diffuse',\r\n        'Env Specular',\r\n        'Emissive',\r\n        'Light Map',\r\n        'Shadow',\r\n        'AO',\r\n\r\n        'Normal Map',\r\n        'Fog',\r\n\r\n        'Tone Mapping',\r\n        'Gamma Correction',\r\n\r\n        'Fresnel',\r\n        'Transmit Diffuse',\r\n        'Transmit Specular',\r\n        'Internal Specular',\r\n        'TT',\r\n    ];\r\n    private strMisc: string[] = [\r\n        'CSM Layer Coloration',\r\n        'Lighting With Albedo',\r\n    ];\r\n\r\n    private compositeModeToggleList: Node[] = [];\r\n    private singleModeToggleList: Node[] = [];\r\n    private miscModeToggleList: Node[] = [];\r\n    private textComponentList: RichText[] = [];\r\n    private labelComponentList: Label[] = [];\r\n    private textContentList: string[] = [];\r\n    private hideButtonLabel: Label;\r\n    start() {\r\n        // get canvas resolution\r\n        const canvas = this.node.parent.getComponent(Canvas);\r\n        if (!canvas) {\r\n            console.error('debug-view-runtime-control should be child of Canvas');\r\n            return;\r\n        }\r\n\r\n        const uiTransform = this.node.parent.getComponent(UITransform);\r\n        const halfScreenWidth = uiTransform.width * 0.5;\r\n        const halfScreenHeight = uiTransform.height * 0.5;\r\n\r\n        let x = -halfScreenWidth + halfScreenWidth * 0.1, y = halfScreenHeight - halfScreenHeight * 0.1;\r\n        const width = 200, height = 20;\r\n\r\n        // new nodes\r\n        const miscNode = this.node.getChildByName('MiscMode');\r\n        const buttonNode = instantiate(miscNode);\r\n        buttonNode.parent = this.node;\r\n        buttonNode.name = 'Buttons';\r\n        const titleNode = instantiate(miscNode);\r\n        titleNode.parent = this.node;\r\n        titleNode.name = 'Titles';\r\n\r\n        // title\r\n        for (let i = 0; i < 2; i++) {\r\n            const newLabel = instantiate(this.EnableAllCompositeModeButton.getChildByName('Label'));\r\n            newLabel.setPosition(x + (i > 0 ? 50 + width * 2 : 150), y, 0.0);\r\n            newLabel.setScale(0.75, 0.75, 0.75);\r\n            newLabel.parent = titleNode;\r\n            const labelComponent = newLabel.getComponent(Label);\r\n            labelComponent.string = i ? '----------Composite Mode----------' : '----------Single Mode----------';\r\n            labelComponent.color = Color.WHITE;\r\n            labelComponent.overflow = 0;\r\n            this.labelComponentList[this.labelComponentList.length] = labelComponent;\r\n        }\r\n\r\n        y -= height;\r\n        // single\r\n        let currentRow = 0;\r\n        for (let i = 0; i < this.strSingle.length; i++, currentRow++) {\r\n            if (i === this.strSingle.length >> 1) {\r\n                x += width;\r\n                currentRow = 0;\r\n            }\r\n            const newNode = i ? instantiate(this.singleModeToggle) : this.singleModeToggle;\r\n            newNode.setPosition(x, y - height * currentRow, 0.0);\r\n            newNode.setScale(0.5, 0.5, 0.5);\r\n            newNode.parent = this.singleModeToggle.parent;\r\n\r\n            const textComponent = newNode.getComponentInChildren(RichText);\r\n            textComponent.string = this.strSingle[i];\r\n            this.textComponentList[this.textComponentList.length] = textComponent;\r\n            this.textContentList[this.textContentList.length] = textComponent.string;\r\n\r\n            newNode.on(Toggle.EventType.TOGGLE, this.toggleSingleMode, this);\r\n\r\n            this.singleModeToggleList[i] = newNode;\r\n        }\r\n\r\n        x += width;\r\n        // buttons\r\n        this.EnableAllCompositeModeButton.setPosition(x + 15, y, 0.0);\r\n        this.EnableAllCompositeModeButton.setScale(0.5, 0.5, 0.5);\r\n        this.EnableAllCompositeModeButton.on(Button.EventType.CLICK, this.enableAllCompositeMode, this);\r\n        this.EnableAllCompositeModeButton.parent = buttonNode;\r\n        let labelComponent = this.EnableAllCompositeModeButton.getComponentInChildren(Label);\r\n        this.labelComponentList[this.labelComponentList.length] = labelComponent;\r\n\r\n        const changeColorButton = instantiate(this.EnableAllCompositeModeButton);\r\n        changeColorButton.setPosition(x + 90, y, 0.0);\r\n        changeColorButton.setScale(0.5, 0.5, 0.5);\r\n        changeColorButton.on(Button.EventType.CLICK, this.changeTextColor, this);\r\n        changeColorButton.parent = buttonNode;\r\n        labelComponent = changeColorButton.getComponentInChildren(Label);\r\n        labelComponent.string = 'TextColor';\r\n        this.labelComponentList[this.labelComponentList.length] = labelComponent;\r\n\r\n        const HideButton = instantiate(this.EnableAllCompositeModeButton);\r\n        HideButton.setPosition(x + 200, y, 0.0);\r\n        HideButton.setScale(0.5, 0.5, 0.5);\r\n        HideButton.on(Button.EventType.CLICK, this.hideUI, this);\r\n        HideButton.parent = this.node.parent;\r\n        labelComponent = HideButton.getComponentInChildren(Label);\r\n        labelComponent.string = 'Hide UI';\r\n        this.labelComponentList[this.labelComponentList.length] = labelComponent;\r\n        this.hideButtonLabel = labelComponent;\r\n\r\n        // misc\r\n        y -= 40;\r\n        for (let i = 0; i < this.strMisc.length; i++) {\r\n            const newNode = instantiate(this.compositeModeToggle);\r\n            newNode.setPosition(x, y - height * i, 0.0);\r\n            newNode.setScale(0.5, 0.5, 0.5);\r\n            newNode.parent = miscNode;\r\n\r\n            const textComponent = newNode.getComponentInChildren(RichText);\r\n            textComponent.string = this.strMisc[i];\r\n            this.textComponentList[this.textComponentList.length] = textComponent;\r\n            this.textContentList[this.textContentList.length] = textComponent.string;\r\n\r\n            const toggleComponent = newNode.getComponent(Toggle);\r\n            toggleComponent.isChecked = i ? true : false;\r\n            newNode.on(Toggle.EventType.TOGGLE, i ? this.toggleLightingWithAlbedo : this.toggleCSMColoration, this);\r\n            this.miscModeToggleList[i] = newNode;\r\n        }\r\n\r\n        // composite\r\n        y -= 150;\r\n        for (let i = 0; i < this.strComposite.length; i++) {\r\n            const newNode = i ? instantiate(this.compositeModeToggle) : this.compositeModeToggle;\r\n            newNode.setPosition(x, y - height * i, 0.0);\r\n            newNode.setScale(0.5, 0.5, 0.5);\r\n            newNode.parent = this.compositeModeToggle.parent;\r\n\r\n            const textComponent = newNode.getComponentInChildren(RichText);\r\n            textComponent.string = this.strComposite[i];\r\n            this.textComponentList[this.textComponentList.length] = textComponent;\r\n            this.textContentList[this.textContentList.length] = textComponent.string;\r\n\r\n            newNode.on(Toggle.EventType.TOGGLE, this.toggleCompositeMode, this);\r\n\r\n            this.compositeModeToggleList[i] = newNode;\r\n        }\r\n    }\r\n\r\n    isTextMatched(textUI, textDescription) : boolean {\r\n        let tempText = new String(textUI);\r\n        const findIndex = tempText.search('>');\r\n        if (findIndex === -1) {\r\n            return textUI === textDescription;\r\n        } else {\r\n            tempText = tempText.substr(findIndex + 1);\r\n            tempText = tempText.substr(0, tempText.search('<'));\r\n            return tempText === textDescription;\r\n        }\r\n    }\r\n    toggleSingleMode(toggle: Toggle) {\r\n        const debugView = director.root!.debugView;\r\n        const textComponent = toggle.getComponentInChildren(RichText);\r\n        for (let i = 0; i < this.strSingle.length; i++) {\r\n            if (this.isTextMatched(textComponent.string, this.strSingle[i])) {\r\n                debugView.singleMode = i;\r\n            }\r\n        }\r\n    }\r\n    toggleCompositeMode(toggle: Toggle) {\r\n        const debugView = director.root!.debugView;\r\n        const textComponent = toggle.getComponentInChildren(RichText);\r\n        for (let i = 0; i < this.strComposite.length; i++) {\r\n            if (this.isTextMatched(textComponent.string, this.strComposite[i])) {\r\n                debugView.enableCompositeMode(i, toggle.isChecked);\r\n            }\r\n        }\r\n    }\r\n    toggleLightingWithAlbedo(toggle: Toggle) {\r\n        const debugView = director.root!.debugView;\r\n        debugView.lightingWithAlbedo = toggle.isChecked;\r\n    }\r\n    toggleCSMColoration(toggle: Toggle) {\r\n        const debugView = director.root!.debugView;\r\n        debugView.csmLayerColoration = toggle.isChecked;\r\n    }\r\n    enableAllCompositeMode(button: Button) {\r\n        const debugView = director.root!.debugView;\r\n        debugView.enableAllCompositeMode(true);\r\n        for (let i = 0; i < this.compositeModeToggleList.length; i++) {\r\n            const toggleComponent = this.compositeModeToggleList[i].getComponent(Toggle);\r\n            toggleComponent.isChecked = true;\r\n        }\r\n\r\n        let toggleComponent = this.miscModeToggleList[0].getComponent(Toggle);\r\n        toggleComponent.isChecked = false;\r\n        debugView.csmLayerColoration = false;\r\n        toggleComponent = this.miscModeToggleList[1].getComponent(Toggle);\r\n        toggleComponent.isChecked = true;\r\n        debugView.lightingWithAlbedo = true;\r\n    }\r\n    hideUI(button: Button) {\r\n        const titleNode = this.node.getChildByName('Titles');\r\n        const activeValue = !titleNode.active;\r\n        this.singleModeToggleList[0].parent.active = activeValue;\r\n        this.miscModeToggleList[0].parent.active = activeValue;\r\n        this.compositeModeToggleList[0].parent.active = activeValue;\r\n        this.EnableAllCompositeModeButton.parent.active = activeValue;\r\n        titleNode.active = activeValue;\r\n        this.hideButtonLabel.string = activeValue ? 'Hide UI' : 'Show UI';\r\n    }\r\n\r\n    private _currentColorIndex = 0;\r\n    private strColor: string[] = [\r\n        '<color=#ffffff>',\r\n        '<color=#000000>',\r\n        '<color=#ff0000>',\r\n        '<color=#00ff00>',\r\n        '<color=#0000ff>',\r\n    ];\r\n    private color: Color[] = [\r\n        Color.WHITE,\r\n        Color.BLACK,\r\n        Color.RED,\r\n        Color.GREEN,\r\n        Color.BLUE,\r\n    ];\r\n    changeTextColor(button: Button) {\r\n        this._currentColorIndex++;\r\n        if (this._currentColorIndex >= this.strColor.length) {\r\n            this._currentColorIndex = 0;\r\n        }\r\n        for (let i = 0; i < this.textComponentList.length; i++) {\r\n            this.textComponentList[i].string = this.strColor[this._currentColorIndex] + this.textContentList[i] + '</color>';\r\n        }\r\n        for (let i = 0; i < this.labelComponentList.length; i++) {\r\n            this.labelComponentList[i].color = this.color[this._currentColorIndex];\r\n        }\r\n    }\r\n\r\n    onLoad() {\r\n    }\r\n    update(deltaTime: number) {\r\n    }\r\n}\r\n"]}