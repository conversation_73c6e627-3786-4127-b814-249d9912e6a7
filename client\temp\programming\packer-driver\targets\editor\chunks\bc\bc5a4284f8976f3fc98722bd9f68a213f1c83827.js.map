{"version": 3, "sources": ["file:///D:/Projects/cute-animals-client/client/assets/app/script/view/game/DragTouchCmpt.ts"], "names": ["_decorator", "Component", "Node", "v2", "CLICK_SPACE", "g<PERSON>elper", "DragTouchType", "ccclass", "property", "DragTouchCmpt", "interactable", "target", "isDownClick", "isFirstDrag", "_temp_vec2_1", "_temp_vec2_2", "init", "node", "on", "EventType", "TOUCH_START", "onTouchStart", "TOUCH_MOVE", "onTouchMove", "TOUCH_END", "onTouchEnd", "TOUCH_CANCEL", "SetSwallowTouches", "clean", "off", "event", "clickTouchId", "getID", "startLocation", "getStartLocation", "location", "getLocation", "mag", "subtract", "length", "onTouchEvent", "DRAG_BEGIN", "DRAG_MOVE", "CLICK", "END"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;;AACzCC,MAAAA,W,iBAAAA,W;;AAEAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,a,iBAAAA,a;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;AAE9B;AACA;AACA;;yBAEqBS,a,GADpBF,O,UAAD,MACqBE,aADrB,SAC2CR,SAD3C,CACqD;AAAA;AAAA;AAAA,eAE1CS,YAF0C,GAElB,IAFkB;AAAA,eAIzCC,MAJyC,GAInB,IAJmB;AAAA,eAKzCC,WALyC,GAKlB,KALkB;AAKZ;AALY,eAMzCC,WANyC,GAMlB,KANkB;AAMZ;AANY,eAQzCC,YARyC,GAQpBX,EAAE,EARkB;AAAA,eASzCY,YATyC,GASpBZ,EAAE,EATkB;AAAA;;AAW1Ca,QAAAA,IAAI,CAACL,MAAD,EAAsB;AAC7B,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKM,IAAL,CAAUC,EAAV,CAAahB,IAAI,CAACiB,SAAL,CAAeC,WAA5B,EAAyC,KAAKC,YAA9C,EAA4D,IAA5D;AACA,eAAKJ,IAAL,CAAUC,EAAV,CAAahB,IAAI,CAACiB,SAAL,CAAeG,UAA5B,EAAwC,KAAKC,WAA7C,EAA0D,IAA1D;AACA,eAAKN,IAAL,CAAUC,EAAV,CAAahB,IAAI,CAACiB,SAAL,CAAeK,SAA5B,EAAuC,KAAKC,UAA5C,EAAwD,IAAxD;AACA,eAAKR,IAAL,CAAUC,EAAV,CAAahB,IAAI,CAACiB,SAAL,CAAeO,YAA5B,EAA0C,KAAKD,UAA/C,EAA2D,IAA3D;AACA,eAAKR,IAAL,CAAUU,iBAAV,CAA4B,KAA5B,EAN6B,CAMM;;AACnC,iBAAO,IAAP;AACH;;AAEMC,QAAAA,KAAK,GAAG;AACX,eAAKX,IAAL,CAAUY,GAAV,CAAc3B,IAAI,CAACiB,SAAL,CAAeC,WAA7B,EAA0C,KAAKC,YAA/C,EAA6D,IAA7D;AACA,eAAKJ,IAAL,CAAUY,GAAV,CAAc3B,IAAI,CAACiB,SAAL,CAAeG,UAA7B,EAAyC,KAAKC,WAA9C,EAA2D,IAA3D;AACA,eAAKN,IAAL,CAAUY,GAAV,CAAc3B,IAAI,CAACiB,SAAL,CAAeK,SAA7B,EAAwC,KAAKC,UAA7C,EAAyD,IAAzD;AACA,eAAKR,IAAL,CAAUY,GAAV,CAAc3B,IAAI,CAACiB,SAAL,CAAeO,YAA7B,EAA2C,KAAKD,UAAhD,EAA4D,IAA5D;AACA,eAAKd,MAAL,GAAc,IAAd;AACH,SA3BgD,CA6BjD;;;AACQU,QAAAA,YAAY,CAACS,KAAD,EAAoB;AACpC,cAAI,CAAC,KAAKpB,YAAN,IAAsB;AAAA;AAAA,kCAAQqB,YAAR,KAAyB,CAAC,CAApD,EAAuD;AACnD;AACH;;AACD;AAAA;AAAA,kCAAQA,YAAR,GAAuBD,KAAK,CAACE,KAAN,EAAvB;AACA,eAAKpB,WAAL,GAAmB,IAAnB;AACA,eAAKC,WAAL,GAAmB,KAAnB;AACH;;AAEOU,QAAAA,WAAW,CAACO,KAAD,EAAoB;AACnC,cAAI,CAAC,KAAKpB,YAAN,IAAsB;AAAA;AAAA,kCAAQqB,YAAR,KAAyBD,KAAK,CAACE,KAAN,EAAnD,EAAkE;AAC9D;AACH;;AACD,gBAAMC,aAAa,GAAGH,KAAK,CAACI,gBAAN,CAAuB,KAAKpB,YAA5B,CAAtB;AACA,gBAAMqB,QAAQ,GAAGL,KAAK,CAACM,WAAN,CAAkB,KAAKrB,YAAvB,CAAjB;AACA,gBAAMsB,GAAG,GAAGJ,aAAa,CAACK,QAAd,CAAuBH,QAAvB,EAAiCI,MAAjC,EAAZ;;AACA,cAAIF,GAAG,GAAG,CAAV,EAAa;AACT,iBAAKzB,WAAL,GAAmB,KAAnB;;AACA,gBAAI,CAAC,KAAKC,WAAV,EAAuB;AACnB,mBAAKA,WAAL,GAAmB,IAAnB;AACA,mBAAKF,MAAL,CAAY6B,YAAZ,CAAyB;AAAA;AAAA,kDAAcC,UAAvC,EAAmDX,KAAnD;AACH;;AACD,iBAAKnB,MAAL,CAAY6B,YAAZ,CAAyB;AAAA;AAAA,gDAAcE,SAAvC,EAAkDZ,KAAlD;AACH;AACJ;;AAEOL,QAAAA,UAAU,CAACK,KAAD,EAAoB;AAClC,cAAI;AAAA;AAAA,kCAAQC,YAAR,KAAyBD,KAAK,CAACE,KAAN,EAAzB,IAA0C,CAAC,KAAKtB,YAApD,EAAkE;AAC9D;AACH;;AACD;AAAA;AAAA,kCAAQqB,YAAR,GAAuB,CAAC,CAAxB;;AACA,cAAI,KAAKnB,WAAT,EAAsB;AAClB,kBAAMqB,aAAa,GAAGH,KAAK,CAACI,gBAAN,CAAuB,KAAKpB,YAA5B,CAAtB;AACA,kBAAMqB,QAAQ,GAAGL,KAAK,CAACM,WAAN,CAAkB,KAAKrB,YAAvB,CAAjB;AACA,kBAAMsB,GAAG,GAAGJ,aAAa,CAACK,QAAd,CAAuBH,QAAvB,EAAiCI,MAAjC,EAAZ;;AACA,gBAAIF,GAAG;AAAA;AAAA,2CAAP,EAAwB;AACpB,mBAAK1B,MAAL,CAAY6B,YAAZ,CAAyB;AAAA;AAAA,kDAAcG,KAAvC,EAA8Cb,KAA9C;AACH;AACJ;;AACD,eAAKnB,MAAL,CAAY6B,YAAZ,CAAyB;AAAA;AAAA,8CAAcI,GAAvC,EAA4Cd,KAA5C;AACH;;AAtEgD,O", "sourcesContent": ["import { _decorator, Component, EventTouch, Node, v2, Vec2 } from \"cc\";\r\nimport { CLICK_SPACE } from \"../../common/constant/Constant\";\r\nimport { IDragTarget } from \"../../common/constant/interface\";\r\nimport { gHelper } from \"../../common/helper/GameHelper\";\r\nimport { DragTouchType } from \"../../common/constant/Enums\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 用于拖动的组建\r\n */\r\n@ccclass\r\nexport default class DragTouchCmpt extends Component {\r\n\r\n    public interactable: boolean = true\r\n\r\n    private target: IDragTarget = null\r\n    private isDownClick: boolean = false //是否按下点击\r\n    private isFirstDrag: boolean = false //是否首次拖动\r\n\r\n    private _temp_vec2_1: Vec2 = v2()\r\n    private _temp_vec2_2: Vec2 = v2()\r\n\r\n    public init(target: IDragTarget) {\r\n        this.target = target\r\n        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this)\r\n        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this)\r\n        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this)\r\n        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this)\r\n        this.node.SetSwallowTouches(false) //默认开启穿透\r\n        return this\r\n    }\r\n\r\n    public clean() {\r\n        this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this)\r\n        this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMove, this)\r\n        this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this)\r\n        this.node.off(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this)\r\n        this.target = null\r\n    }\r\n\r\n    // 触摸开始\r\n    private onTouchStart(event: EventTouch) {\r\n        if (!this.interactable || gHelper.clickTouchId !== -1) {\r\n            return\r\n        }\r\n        gHelper.clickTouchId = event.getID()\r\n        this.isDownClick = true\r\n        this.isFirstDrag = false\r\n    }\r\n\r\n    private onTouchMove(event: EventTouch) {\r\n        if (!this.interactable || gHelper.clickTouchId !== event.getID()) {\r\n            return\r\n        }\r\n        const startLocation = event.getStartLocation(this._temp_vec2_1)\r\n        const location = event.getLocation(this._temp_vec2_2)\r\n        const mag = startLocation.subtract(location).length()\r\n        if (mag > 4) {\r\n            this.isDownClick = false\r\n            if (!this.isFirstDrag) {\r\n                this.isFirstDrag = true\r\n                this.target.onTouchEvent(DragTouchType.DRAG_BEGIN, event)\r\n            }\r\n            this.target.onTouchEvent(DragTouchType.DRAG_MOVE, event)\r\n        }\r\n    }\r\n\r\n    private onTouchEnd(event: EventTouch) {\r\n        if (gHelper.clickTouchId !== event.getID() || !this.interactable) {\r\n            return\r\n        }\r\n        gHelper.clickTouchId = -1\r\n        if (this.isDownClick) {\r\n            const startLocation = event.getStartLocation(this._temp_vec2_1)\r\n            const location = event.getLocation(this._temp_vec2_2)\r\n            const mag = startLocation.subtract(location).length()\r\n            if (mag <= CLICK_SPACE) {\r\n                this.target.onTouchEvent(DragTouchType.CLICK, event)\r\n            }\r\n        }\r\n        this.target.onTouchEvent(DragTouchType.END, event)\r\n    }\r\n}"]}