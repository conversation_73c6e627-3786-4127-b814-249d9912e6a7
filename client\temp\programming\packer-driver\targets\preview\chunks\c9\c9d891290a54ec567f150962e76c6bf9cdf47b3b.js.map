{"version": 3, "sources": ["file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts"], "names": ["_decorator", "Camera", "CCBoolean", "CCFloat", "CCInteger", "Component", "Material", "rendering", "Texture2D", "EDITOR", "BloomType", "fillRequiredPipelineSettings", "makePipelineSettings", "ccclass", "disallowMultiple", "executeInEditMode", "menu", "property", "requireComponent", "type", "BuiltinPipelineSettings", "displayName", "group", "id", "name", "style", "range", "tooltip", "slide", "min", "getPipelineSettings", "_settings", "onEnable", "cameraComponent", "getComponent", "camera", "pipelineSettings", "_tryEnableEditorPreview", "onDisable", "_disableEditorPreview", "editorPreview", "_editorPreview", "v", "undefined", "setEditorPipelineSettings", "current", "getEditorPipelineSettings", "Msaa<PERSON>nable", "msaa", "enabled", "value", "msaaSampleCount", "Math", "ceil", "log2", "max", "sampleCount", "shadingScaleEnable", "enableShadingScale", "shadingScale", "bloomEnable", "bloom", "bloomType", "kawaseBloomMaterial", "kawaseFilterMaterial", "mipmapBloomMaterial", "mipmapFilterMaterial", "bloomEnableAlphaMask", "enableAlphaMask", "bloomIterations", "iterations", "bloom<PERSON><PERSON><PERSON>old", "threshold", "bloomIntensity", "intensity", "colorGradingEnable", "colorGrading", "colorGradingMaterial", "material", "colorGradingContribute", "contribute", "colorGradingMap", "val", "fxaaEnable", "fxaa", "fxaaMaterial", "fsrEnable", "fsr", "fsrMaterial", "fsrSharpness", "sharpness", "toneMappingMaterial", "toneMapping"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBIA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AACnDC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AAGhBC,MAAAA,M,UAAAA,M;;AAGLC,MAAAA,S,iBAAAA,S;AACAC,MAAAA,4B,iBAAAA,4B;AAA8BC,MAAAA,oB,iBAAAA,oB;;;;;;AAjClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;OAcM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,gBAAX;AAA6BC,QAAAA,iBAA7B;AAAgDC,QAAAA,IAAhD;AAAsDC,QAAAA,QAAtD;AAAgEC,QAAAA,gBAAhE;AAAkFC,QAAAA;AAAlF,O,GAA2FnB,U;;yCAOpFoB,uB,WALZP,OAAO,CAAC,yBAAD,C,UACPG,IAAI,CAAC,mCAAD,C,UACJE,gBAAgB,CAACjB,MAAD,C,UAkCZgB,QAAQ,CAACf,SAAD,C,UAGRe,QAAQ,CAAC;AACNI,QAAAA,WAAW,EAAE,+BADP;AAENF,QAAAA,IAAI,EAAEjB;AAFA,OAAD,C,UAkCRe,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,MAAN;AAAcC,UAAAA,IAAI,EAAE;AAApB,SADD;AAENL,QAAAA,IAAI,EAAEjB;AAFA,OAAD,C,UAcRe,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,MAAN;AAAcC,UAAAA,IAAI,EAAE,2BAApB;AAAiDC,UAAAA,KAAK,EAAE;AAAxD,SADD;AAENN,QAAAA,IAAI,EAAEf,SAFA;AAGNsB,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP;AAHD,OAAD,C,UAkBRT,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE,cAA5B;AAA4CC,UAAAA,KAAK,EAAE;AAAnD,SADD;AAENN,QAAAA,IAAI,EAAEjB;AAFA,OAAD,C,UAcRe,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,+BADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,cAAN;AAAsBC,UAAAA,IAAI,EAAE;AAA5B,SAFD;AAGNL,QAAAA,IAAI,EAAEhB,OAHA;AAINuB,QAAAA,KAAK,EAAE,CAAC,IAAD,EAAO,CAAP,EAAU,IAAV,CAJD;AAKNE,QAAAA,KAAK,EAAE;AALD,OAAD,C,WAkBRX,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SADD;AAENN,QAAAA,IAAI,EAAEjB;AAFA,OAAD,C,WAcRiB,IAAI;AAAA;AAAA,iC,WACJF,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD;AADD,OAAD,C,WAcRR,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SADD;AAENN,QAAAA,IAAI,EAAEb;AAFA,OAAD,C,WAiBRW,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SADD;AAENN,QAAAA,IAAI,EAAEb;AAFA,OAAD,C,WAiBRW,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,4BADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SAFD;AAGNN,QAAAA,IAAI,EAAEjB;AAHA,OAAD,C,WAeRe,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,uBADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SAFD;AAGNN,QAAAA,IAAI,EAAEf,SAHA;AAINsB,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAJD;AAKNE,QAAAA,KAAK,EAAE;AALD,OAAD,C,WAiBRX,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,sBADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD,SAFD;AAGNN,QAAAA,IAAI,EAAEhB,OAHA;AAIN0B,QAAAA,GAAG,EAAE;AAJC,OAAD,C,WAaRV,IAAI,CAAChB,OAAD,C,WACJc,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,OAAN;AAAeC,UAAAA,IAAI,EAAE,wBAArB;AAA+CC,UAAAA,KAAK,EAAE;AAAtD;AADD,OAAD,C,WAcRR,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,eAAN;AAAuBC,UAAAA,IAAI,EAAE,qCAA7B;AAAoEC,UAAAA,KAAK,EAAE;AAA3E,SADD;AAENN,QAAAA,IAAI,EAAEjB;AAFA,OAAD,C,WAcRe,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,eAAN;AAAuBC,UAAAA,IAAI,EAAE,qCAA7B;AAAoEC,UAAAA,KAAK,EAAE;AAA3E,SADD;AAENN,QAAAA,IAAI,EAAEb;AAFA,OAAD,C,WAiBRW,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,+BADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,eAAN;AAAuBC,UAAAA,IAAI,EAAE,qCAA7B;AAAoEC,UAAAA,KAAK,EAAE;AAA3E,SAFD;AAGNN,QAAAA,IAAI,EAAEhB,OAHA;AAINuB,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,IAAP,CAJD;AAKNE,QAAAA,KAAK,EAAE;AALD,OAAD,C,WAcRX,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAE,gCADH;AAENL,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,eAAN;AAAuBC,UAAAA,IAAI,EAAE,qCAA7B;AAAoEC,UAAAA,KAAK,EAAE;AAA3E,SAFD;AAGNN,QAAAA,IAAI,EAAEX;AAHA,OAAD,C,WAgBRS,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,MAAN;AAAcC,UAAAA,IAAI,EAAE,iDAApB;AAAuEC,UAAAA,KAAK,EAAE;AAA9E,SADD;AAENN,QAAAA,IAAI,EAAEjB;AAFA,OAAD,C,WAcRe,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,MAAN;AAAcC,UAAAA,IAAI,EAAE,iDAApB;AAAuEC,UAAAA,KAAK,EAAE;AAA9E,SADD;AAENN,QAAAA,IAAI,EAAEb;AAFA,OAAD,C,WAkBRW,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,KAAN;AAAaC,UAAAA,IAAI,EAAE,6BAAnB;AAAkDC,UAAAA,KAAK,EAAE;AAAzD,SADD;AAENN,QAAAA,IAAI,EAAEjB;AAFA,OAAD,C,WAcRe,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,KAAN;AAAaC,UAAAA,IAAI,EAAE,6BAAnB;AAAkDC,UAAAA,KAAK,EAAE;AAAzD,SADD;AAENN,QAAAA,IAAI,EAAEb;AAFA,OAAD,C,WAiBRW,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,KAAN;AAAaC,UAAAA,IAAI,EAAE,6BAAnB;AAAkDC,UAAAA,KAAK,EAAE;AAAzD,SADD;AAENN,QAAAA,IAAI,EAAEhB,OAFA;AAGNuB,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,IAAP,CAHD;AAINE,QAAAA,KAAK,EAAE;AAJD,OAAD,C,WAaRX,QAAQ,CAAC;AACNK,QAAAA,KAAK,EAAE;AAAEC,UAAAA,EAAE,EAAE,aAAN;AAAqBC,UAAAA,IAAI,EAAE,aAA3B;AAA0CC,UAAAA,KAAK,EAAE;AAAjD,SADD;AAENN,QAAAA,IAAI,EAAEb;AAFA,OAAD,C,8CA1YZQ,gB,UACAC,iB,qBAJD,MAKaK,uBALb,SAK6Cf,SAL7C,CAKuD;AAAA;AAAA;;AAAA;;AA8BnD;AA9BmD;AAAA;;AAInDyB,QAAAA,mBAAmB,GAAqB;AACpC,iBAAO,KAAKC,SAAZ;AACH,SANkD,CAQnD;;;AACAC,QAAAA,QAAQ,GAAS;AACb;AAAA;AAAA,4EAA6B,KAAKD,SAAlC;AACA,cAAME,eAAe,GAAG,KAAKC,YAAL,CAAkBjC,MAAlB,CAAxB;AACA,cAAMkC,MAAM,GAAGF,eAAe,CAACE,MAA/B;AACAA,UAAAA,MAAM,CAACC,gBAAP,GAA0B,KAAKL,SAA/B;;AAEA,cAAItB,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACDC,QAAAA,SAAS,GAAS;AACd,cAAML,eAAe,GAAG,KAAKC,YAAL,CAAkBjC,MAAlB,CAAxB;AACA,cAAMkC,MAAM,GAAGF,eAAe,CAACE,MAA/B;;AACA,cAAIA,MAAJ,EAAY;AACRA,YAAAA,MAAM,CAACC,gBAAP,GAA0B,IAA1B;AACH;;AACD,cAAI3B,MAAJ,EAAY;AACR,iBAAK8B,qBAAL;AACH;AACJ;;AAUgB,YAAbC,aAAa,GAAY;AACzB,iBAAO,KAAKC,cAAZ;AACH;;AACgB,YAAbD,aAAa,CAACE,CAAD,EAAa;AAC1B,eAAKD,cAAL,GAAsBC,CAAtB;;AACA,cAAIjC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACMA,QAAAA,uBAAuB,GAAS;AACnC,cAAI9B,SAAS,KAAKoC,SAAlB,EAA6B;AACzB;AACH;;AACD,cAAI,KAAKF,cAAT,EAAyB;AACrBlC,YAAAA,SAAS,CAACqC,yBAAV,CAAoC,KAAKb,SAAzC;AACH,WAFD,MAEO;AACH,iBAAKQ,qBAAL;AACH;AACJ;;AACMA,QAAAA,qBAAqB,GAAS;AACjC,cAAIhC,SAAS,KAAKoC,SAAlB,EAA6B;AACzB;AACH;;AACD,cAAME,OAAO,GAAGtC,SAAS,CAACuC,yBAAV,EAAhB;;AACA,cAAID,OAAO,KAAK,KAAKd,SAArB,EAAgC;AAC5BxB,YAAAA,SAAS,CAACqC,yBAAV,CAAoC,IAApC;AACH;AACJ,SAjEkD,CAmEnD;;;AAKc,YAAVG,UAAU,GAAY;AACtB,iBAAO,KAAKhB,SAAL,CAAeiB,IAAf,CAAoBC,OAA3B;AACH;;AACa,YAAVF,UAAU,CAACG,KAAD,EAAiB;AAC3B,eAAKnB,SAAL,CAAeiB,IAAf,CAAoBC,OAApB,GAA8BC,KAA9B;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AAOkB,YAAfc,eAAe,CAACD,KAAD,EAAgB;AAC/BA,UAAAA,KAAK,GAAG,KAAKE,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,IAAL,CAAUF,IAAI,CAACG,GAAL,CAASL,KAAT,EAAgB,CAAhB,CAAV,CAAV,CAAb;AACAA,UAAAA,KAAK,GAAGE,IAAI,CAACvB,GAAL,CAASqB,KAAT,EAAgB,CAAhB,CAAR;AACA,eAAKnB,SAAL,CAAeiB,IAAf,CAAoBQ,WAApB,GAAkCN,KAAlC;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACkB,YAAfc,eAAe,GAAW;AAC1B,iBAAO,KAAKpB,SAAL,CAAeiB,IAAf,CAAoBQ,WAA3B;AACH,SAjGkD,CAmGnD;;;AAKsB,YAAlBC,kBAAkB,CAACP,KAAD,EAAiB;AACnC,eAAKnB,SAAL,CAAe2B,kBAAf,GAAoCR,KAApC;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACqB,YAAlBoB,kBAAkB,GAAY;AAC9B,iBAAO,KAAK1B,SAAL,CAAe2B,kBAAtB;AACH;;AASe,YAAZC,YAAY,CAACT,KAAD,EAAgB;AAC5B,eAAKnB,SAAL,CAAe4B,YAAf,GAA8BT,KAA9B;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACe,YAAZsB,YAAY,GAAW;AACvB,iBAAO,KAAK5B,SAAL,CAAe4B,YAAtB;AACH,SAjIkD,CAmInD;;;AAKe,YAAXC,WAAW,CAACV,KAAD,EAAiB;AAC5B,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBZ,OAArB,GAA+BC,KAA/B;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACc,YAAXuB,WAAW,GAAY;AACvB,iBAAO,KAAK7B,SAAL,CAAe8B,KAAf,CAAqBZ,OAA5B;AACH;;AAMY,YAATa,SAAS,CAACZ,KAAD,EAAmB;AAC5B,eAAKnB,SAAL,CAAe8B,KAAf,CAAqB1C,IAArB,GAA4B+B,KAA5B;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AAEY,YAATyB,SAAS,GAAc;AACvB,iBAAO,KAAK/B,SAAL,CAAe8B,KAAf,CAAqB1C,IAA5B;AACH;;AAMsB,YAAnB4C,mBAAmB,CAACb,KAAD,EAAkB;AACrC,cAAI,KAAKnB,SAAL,CAAe8B,KAAf,CAAqBG,oBAArB,KAA8Cd,KAAlD,EAAyD;AACrD;AACH;;AACD,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBG,oBAArB,GAA4Cd,KAA5C;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACsB,YAAnB0B,mBAAmB,GAAa;AAChC,iBAAO,KAAKhC,SAAL,CAAe8B,KAAf,CAAqBG,oBAA5B;AACH;;AAMsB,YAAnBC,mBAAmB,CAACf,KAAD,EAAkB;AACrC,cAAI,KAAKnB,SAAL,CAAe8B,KAAf,CAAqBK,oBAArB,KAA8ChB,KAAlD,EAAyD;AACrD;AACH;;AACD,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBK,oBAArB,GAA4ChB,KAA5C;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACsB,YAAnB4B,mBAAmB,GAAa;AAChC,iBAAO,KAAKlC,SAAL,CAAe8B,KAAf,CAAqBK,oBAA5B;AACH;;AAOuB,YAApBC,oBAAoB,CAACjB,KAAD,EAAiB;AACrC,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBO,eAArB,GAAuClB,KAAvC;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACuB,YAApB8B,oBAAoB,GAAY;AAChC,iBAAO,KAAKpC,SAAL,CAAe8B,KAAf,CAAqBO,eAA5B;AACH;;AASkB,YAAfC,eAAe,CAACnB,KAAD,EAAgB;AAC/B,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBS,UAArB,GAAkCpB,KAAlC;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACkB,YAAfgC,eAAe,GAAW;AAC1B,iBAAO,KAAKtC,SAAL,CAAe8B,KAAf,CAAqBS,UAA5B;AACH;;AAQiB,YAAdC,cAAc,CAACrB,KAAD,EAAgB;AAC9B,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBW,SAArB,GAAiCtB,KAAjC;AACH;;AACiB,YAAdqB,cAAc,GAAW;AACzB,iBAAO,KAAKxC,SAAL,CAAe8B,KAAf,CAAqBW,SAA5B;AACH;;AAMiB,YAAdC,cAAc,CAACvB,KAAD,EAAgB;AAC9B,eAAKnB,SAAL,CAAe8B,KAAf,CAAqBa,SAArB,GAAiCxB,KAAjC;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACiB,YAAdoC,cAAc,GAAW;AACzB,iBAAO,KAAK1C,SAAL,CAAe8B,KAAf,CAAqBa,SAA5B;AACH,SA5PkD,CA8PnD;;;AAKsB,YAAlBC,kBAAkB,CAACzB,KAAD,EAAiB;AACnC,eAAKnB,SAAL,CAAe6C,YAAf,CAA4B3B,OAA5B,GAAsCC,KAAtC;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACqB,YAAlBsC,kBAAkB,GAAY;AAC9B,iBAAO,KAAK5C,SAAL,CAAe6C,YAAf,CAA4B3B,OAAnC;AACH;;AAMuB,YAApB4B,oBAAoB,CAAC3B,KAAD,EAAkB;AACtC,cAAI,KAAKnB,SAAL,CAAe6C,YAAf,CAA4BE,QAA5B,KAAyC5B,KAA7C,EAAoD;AAChD;AACH;;AACD,eAAKnB,SAAL,CAAe6C,YAAf,CAA4BE,QAA5B,GAAuC5B,KAAvC;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACuB,YAApBwC,oBAAoB,GAAa;AACjC,iBAAO,KAAK9C,SAAL,CAAe6C,YAAf,CAA4BE,QAAnC;AACH;;AASyB,YAAtBC,sBAAsB,CAAC7B,KAAD,EAAgB;AACtC,eAAKnB,SAAL,CAAe6C,YAAf,CAA4BI,UAA5B,GAAyC9B,KAAzC;AACH;;AACyB,YAAtB6B,sBAAsB,GAAW;AACjC,iBAAO,KAAKhD,SAAL,CAAe6C,YAAf,CAA4BI,UAAnC;AACH;;AAOkB,YAAfC,eAAe,CAACC,GAAD,EAAiB;AAChC,eAAKnD,SAAL,CAAe6C,YAAf,CAA4BK,eAA5B,GAA8CC,GAA9C;;AACA,cAAIzE,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACkB,YAAf4C,eAAe,GAAc;AAC7B,iBAAO,KAAKlD,SAAL,CAAe6C,YAAf,CAA4BK,eAAnC;AACH,SAzTkD,CA2TnD;;;AAKc,YAAVE,UAAU,CAACjC,KAAD,EAAiB;AAC3B,eAAKnB,SAAL,CAAeqD,IAAf,CAAoBnC,OAApB,GAA8BC,KAA9B;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACa,YAAV8C,UAAU,GAAY;AACtB,iBAAO,KAAKpD,SAAL,CAAeqD,IAAf,CAAoBnC,OAA3B;AACH;;AAMe,YAAZoC,YAAY,CAACnC,KAAD,EAAkB;AAC9B,cAAI,KAAKnB,SAAL,CAAeqD,IAAf,CAAoBN,QAApB,KAAiC5B,KAArC,EAA4C;AACxC;AACH;;AACD,eAAKnB,SAAL,CAAeqD,IAAf,CAAoBN,QAApB,GAA+B5B,KAA/B;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACe,YAAZgD,YAAY,GAAa;AACzB,iBAAO,KAAKtD,SAAL,CAAeqD,IAAf,CAAoBN,QAA3B;AACH,SAzVkD,CA2VnD;;;AAKa,YAATQ,SAAS,CAACpC,KAAD,EAAiB;AAC1B,eAAKnB,SAAL,CAAewD,GAAf,CAAmBtC,OAAnB,GAA6BC,KAA7B;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACY,YAATiD,SAAS,GAAY;AACrB,iBAAO,KAAKvD,SAAL,CAAewD,GAAf,CAAmBtC,OAA1B;AACH;;AAMc,YAAXuC,WAAW,CAACtC,KAAD,EAAkB;AAC7B,cAAI,KAAKnB,SAAL,CAAewD,GAAf,CAAmBT,QAAnB,KAAgC5B,KAApC,EAA2C;AACvC;AACH;;AACD,eAAKnB,SAAL,CAAewD,GAAf,CAAmBT,QAAnB,GAA8B5B,KAA9B;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACc,YAAXmD,WAAW,GAAa;AACxB,iBAAO,KAAKzD,SAAL,CAAewD,GAAf,CAAmBT,QAA1B;AACH;;AAQe,YAAZW,YAAY,CAACvC,KAAD,EAAgB;AAC5B,eAAKnB,SAAL,CAAewD,GAAf,CAAmBG,SAAnB,GAA+BxC,KAA/B;AACH;;AACe,YAAZuC,YAAY,GAAW;AACvB,iBAAO,KAAK1D,SAAL,CAAewD,GAAf,CAAmBG,SAA1B;AACH;;AAMsB,YAAnBC,mBAAmB,CAACzC,KAAD,EAAkB;AACrC,cAAI,KAAKnB,SAAL,CAAe6D,WAAf,CAA2Bd,QAA3B,KAAwC5B,KAA5C,EAAmD;AAC/C;AACH;;AACD,eAAKnB,SAAL,CAAe6D,WAAf,CAA2Bd,QAA3B,GAAsC5B,KAAtC;;AACA,cAAIzC,MAAJ,EAAY;AACR,iBAAK4B,uBAAL;AACH;AACJ;;AACsB,YAAnBsD,mBAAmB,GAAa;AAChC,iBAAO,KAAK5D,SAAL,CAAe6D,WAAf,CAA2Bd,QAAlC;AACH;;AAvZkD,O,4EAClD7D,Q;;;;;iBAC8C;AAAA;AAAA,6D;;;;;;;iBA8BpB,K", "sourcesContent": ["/*\r\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\r\n\r\n https://www.cocos.com/\r\n\r\n Permission is hereby granted, free of charge, to any person obtaining a copy\r\n of this software and associated documentation files (the \"Software\"), to deal\r\n in the Software without restriction, including without limitation the rights to\r\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\r\n of the Software, and to permit persons to whom the Software is furnished to do so,\r\n subject to the following conditions:\r\n\r\n The above copyright notice and this permission notice shall be included in\r\n all copies or substantial portions of the Software.\r\n\r\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\n THE SOFTWARE.\r\n*/\r\n\r\nimport {\r\n    _decorator, Camera, CCBoolean, CCFloat, CCInteger, Component,\r\n    Material, rendering, Texture2D,\r\n} from 'cc';\r\n\r\nimport { EDITOR } from 'cc/env';\r\n\r\nimport {\r\n    BloomType,\r\n    fillRequiredPipelineSettings, makePipelineSettings, PipelineSettings,\r\n} from './builtin-pipeline-types';\r\n\r\nconst { ccclass, disallowMultiple, executeInEditMode, menu, property, requireComponent, type } = _decorator;\r\n\r\n@ccclass('BuiltinPipelineSettings')\r\n@menu('Rendering/BuiltinPipelineSettings')\r\n@requireComponent(Camera)\r\n@disallowMultiple\r\n@executeInEditMode\r\nexport class BuiltinPipelineSettings extends Component {\r\n    @property\r\n    private readonly _settings: PipelineSettings = makePipelineSettings();\r\n\r\n    getPipelineSettings(): PipelineSettings {\r\n        return this._settings;\r\n    }\r\n\r\n    // Enable/Disable\r\n    onEnable(): void {\r\n        fillRequiredPipelineSettings(this._settings);\r\n        const cameraComponent = this.getComponent(Camera)!;\r\n        const camera = cameraComponent.camera;\r\n        camera.pipelineSettings = this._settings;\r\n\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    onDisable(): void {\r\n        const cameraComponent = this.getComponent(Camera)!;\r\n        const camera = cameraComponent.camera;\r\n        if (camera) {\r\n            camera.pipelineSettings = null;\r\n        }\r\n        if (EDITOR) {\r\n            this._disableEditorPreview();\r\n        }\r\n    }\r\n\r\n    // Editor Preview\r\n    @property(CCBoolean)\r\n    protected _editorPreview = false;\r\n\r\n    @property({\r\n        displayName: 'Editor Preview (Experimental)',\r\n        type: CCBoolean,\r\n    })\r\n    get editorPreview(): boolean {\r\n        return this._editorPreview;\r\n    }\r\n    set editorPreview(v: boolean) {\r\n        this._editorPreview = v;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    public _tryEnableEditorPreview(): void {\r\n        if (rendering === undefined) {\r\n            return;\r\n        }\r\n        if (this._editorPreview) {\r\n            rendering.setEditorPipelineSettings(this._settings);\r\n        } else {\r\n            this._disableEditorPreview();\r\n        }\r\n    }\r\n    public _disableEditorPreview(): void {\r\n        if (rendering === undefined) {\r\n            return;\r\n        }\r\n        const current = rendering.getEditorPipelineSettings() as PipelineSettings | null;\r\n        if (current === this._settings) {\r\n            rendering.setEditorPipelineSettings(null);\r\n        }\r\n    }\r\n\r\n    // MSAA\r\n    @property({\r\n        group: { id: 'MSAA', name: 'Multisample Anti-Aliasing' },\r\n        type: CCBoolean,\r\n    })\r\n    get MsaaEnable(): boolean {\r\n        return this._settings.msaa.enabled;\r\n    }\r\n    set MsaaEnable(value: boolean) {\r\n        this._settings.msaa.enabled = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'MSAA', name: 'Multisample Anti-Aliasing', style: 'section' },\r\n        type: CCInteger,\r\n        range: [2, 4, 2],\r\n    })\r\n    set msaaSampleCount(value: number) {\r\n        value = 2 ** Math.ceil(Math.log2(Math.max(value, 2)));\r\n        value = Math.min(value, 4);\r\n        this._settings.msaa.sampleCount = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get msaaSampleCount(): number {\r\n        return this._settings.msaa.sampleCount;\r\n    }\r\n\r\n    // Shading Scale\r\n    @property({\r\n        group: { id: 'ShadingScale', name: 'ShadingScale', style: 'section' },\r\n        type: CCBoolean,\r\n    })\r\n    set shadingScaleEnable(value: boolean) {\r\n        this._settings.enableShadingScale = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get shadingScaleEnable(): boolean {\r\n        return this._settings.enableShadingScale;\r\n    }\r\n\r\n    @property({\r\n        tooltip: 'i18n:postprocess.shadingScale',\r\n        group: { id: 'ShadingScale', name: 'ShadingScale' },\r\n        type: CCFloat,\r\n        range: [0.01, 4, 0.01],\r\n        slide: true,\r\n    })\r\n    set shadingScale(value: number) {\r\n        this._settings.shadingScale = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get shadingScale(): number {\r\n        return this._settings.shadingScale;\r\n    }\r\n\r\n    // Bloom\r\n    @property({\r\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\r\n        type: CCBoolean,\r\n    })\r\n    set bloomEnable(value: boolean) {\r\n        this._settings.bloom.enabled = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get bloomEnable(): boolean {\r\n        return this._settings.bloom.enabled;\r\n    }\r\n\r\n    @type(BloomType)\r\n    @property({\r\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\r\n    })\r\n    set bloomType(value: BloomType) {\r\n        this._settings.bloom.type = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n\r\n    get bloomType(): BloomType {\r\n        return this._settings.bloom.type;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\r\n        type: Material,\r\n    })\r\n    set kawaseBloomMaterial(value: Material) {\r\n        if (this._settings.bloom.kawaseFilterMaterial === value) {\r\n            return;\r\n        }\r\n        this._settings.bloom.kawaseFilterMaterial = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get kawaseBloomMaterial(): Material {\r\n        return this._settings.bloom.kawaseFilterMaterial!;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\r\n        type: Material,\r\n    })\r\n    set mipmapBloomMaterial(value: Material) {\r\n        if (this._settings.bloom.mipmapFilterMaterial === value) {\r\n            return;\r\n        }\r\n        this._settings.bloom.mipmapFilterMaterial = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get mipmapBloomMaterial(): Material {\r\n        return this._settings.bloom.mipmapFilterMaterial!;\r\n    }\r\n\r\n    @property({\r\n        tooltip: 'i18n:bloom.enableAlphaMask',\r\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\r\n        type: CCBoolean,\r\n    })\r\n    set bloomEnableAlphaMask(value: boolean) {\r\n        this._settings.bloom.enableAlphaMask = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get bloomEnableAlphaMask(): boolean {\r\n        return this._settings.bloom.enableAlphaMask;\r\n    }\r\n\r\n    @property({\r\n        tooltip: 'i18n:bloom.iterations',\r\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\r\n        type: CCInteger,\r\n        range: [1, 6, 1],\r\n        slide: true,\r\n    })\r\n    set bloomIterations(value: number) {\r\n        this._settings.bloom.iterations = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get bloomIterations(): number {\r\n        return this._settings.bloom.iterations;\r\n    }\r\n\r\n    @property({\r\n        tooltip: 'i18n:bloom.threshold',\r\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\r\n        type: CCFloat,\r\n        min: 0,\r\n    })\r\n    set bloomThreshold(value: number) {\r\n        this._settings.bloom.threshold = value;\r\n    }\r\n    get bloomThreshold(): number {\r\n        return this._settings.bloom.threshold;\r\n    }\r\n\r\n    @type(CCFloat)\r\n    @property({\r\n        group: { id: 'Bloom', name: 'Bloom (PostProcessing)', style: 'section' },\r\n    })\r\n    set bloomIntensity(value: number) {\r\n        this._settings.bloom.intensity = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get bloomIntensity(): number {\r\n        return this._settings.bloom.intensity;\r\n    }\r\n\r\n    // Color Grading (LDR)\r\n    @property({\r\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\r\n        type: CCBoolean,\r\n    })\r\n    set colorGradingEnable(value: boolean) {\r\n        this._settings.colorGrading.enabled = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get colorGradingEnable(): boolean {\r\n        return this._settings.colorGrading.enabled;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\r\n        type: Material,\r\n    })\r\n    set colorGradingMaterial(value: Material) {\r\n        if (this._settings.colorGrading.material === value) {\r\n            return;\r\n        }\r\n        this._settings.colorGrading.material = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get colorGradingMaterial(): Material {\r\n        return this._settings.colorGrading.material!;\r\n    }\r\n\r\n    @property({\r\n        tooltip: 'i18n:color_grading.contribute',\r\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\r\n        type: CCFloat,\r\n        range: [0, 1, 0.01],\r\n        slide: true,\r\n    })\r\n    set colorGradingContribute(value: number) {\r\n        this._settings.colorGrading.contribute = value;\r\n    }\r\n    get colorGradingContribute(): number {\r\n        return this._settings.colorGrading.contribute;\r\n    }\r\n\r\n    @property({\r\n        tooltip: 'i18n:color_grading.originalMap',\r\n        group: { id: 'Color Grading', name: 'ColorGrading (LDR) (PostProcessing)', style: 'section' },\r\n        type: Texture2D,\r\n    })\r\n    set colorGradingMap(val: Texture2D) {\r\n        this._settings.colorGrading.colorGradingMap = val;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get colorGradingMap(): Texture2D {\r\n        return this._settings.colorGrading.colorGradingMap!;\r\n    }\r\n\r\n    // FXAA\r\n    @property({\r\n        group: { id: 'FXAA', name: 'Fast Approximate Anti-Aliasing (PostProcessing)', style: 'section' },\r\n        type: CCBoolean,\r\n    })\r\n    set fxaaEnable(value: boolean) {\r\n        this._settings.fxaa.enabled = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get fxaaEnable(): boolean {\r\n        return this._settings.fxaa.enabled;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'FXAA', name: 'Fast Approximate Anti-Aliasing (PostProcessing)', style: 'section' },\r\n        type: Material,\r\n    })\r\n    set fxaaMaterial(value: Material) {\r\n        if (this._settings.fxaa.material === value) {\r\n            return;\r\n        }\r\n        this._settings.fxaa.material = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get fxaaMaterial(): Material {\r\n        return this._settings.fxaa.material!;\r\n    }\r\n\r\n    // FSR\r\n    @property({\r\n        group: { id: 'FSR', name: 'FidelityFX Super Resolution', style: 'section' },\r\n        type: CCBoolean,\r\n    })\r\n    set fsrEnable(value: boolean) {\r\n        this._settings.fsr.enabled = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get fsrEnable(): boolean {\r\n        return this._settings.fsr.enabled;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'FSR', name: 'FidelityFX Super Resolution', style: 'section' },\r\n        type: Material,\r\n    })\r\n    set fsrMaterial(value: Material) {\r\n        if (this._settings.fsr.material === value) {\r\n            return;\r\n        }\r\n        this._settings.fsr.material = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get fsrMaterial(): Material {\r\n        return this._settings.fsr.material!;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'FSR', name: 'FidelityFX Super Resolution', style: 'section' },\r\n        type: CCFloat,\r\n        range: [0, 1, 0.01],\r\n        slide: true,\r\n    })\r\n    set fsrSharpness(value: number) {\r\n        this._settings.fsr.sharpness = value;\r\n    }\r\n    get fsrSharpness(): number {\r\n        return this._settings.fsr.sharpness;\r\n    }\r\n\r\n    @property({\r\n        group: { id: 'ToneMapping', name: 'ToneMapping', style: 'section' },\r\n        type: Material,\r\n    })\r\n    set toneMappingMaterial(value: Material) {\r\n        if (this._settings.toneMapping.material === value) {\r\n            return;\r\n        }\r\n        this._settings.toneMapping.material = value;\r\n        if (EDITOR) {\r\n            this._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    get toneMappingMaterial(): Material {\r\n        return this._settings.toneMapping.material!;\r\n    }\r\n}\r\n"]}