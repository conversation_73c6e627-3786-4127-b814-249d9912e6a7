{"version": 3, "sources": ["file:///C:/ProgramData/cocos/editors/Creator/3.8.7/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts"], "names": ["_decorator", "assert", "Component", "BuiltinPipelineSettings", "EDITOR", "ccclass", "disallowMultiple", "executeInEditMode", "menu", "requireComponent", "BuiltinPipelinePassBuilder", "_parent", "_settings", "getConfigOrder", "getRenderOrder", "onEnable", "getComponent", "getPipelineSettings", "Object", "prototype", "hasOwnProperty", "call", "defineProperty", "value", "configurable", "enumerable", "writable", "_passes", "undefined", "push", "_tryEnableEditorPreview", "onDisable", "passes", "idx", "indexOf", "splice"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAyBIA,MAAAA,U,OAAAA,U;AACAC,MAAAA,M,OAAAA,M;AACAC,MAAAA,S,OAAAA,S;;AAIKC,MAAAA,uB,iBAAAA,uB;;AAEAC,MAAAA,M,UAAAA,M;;;;;;AAjCT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;OAaM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,gBAAX;AAA6BC,QAAAA,iBAA7B;AAAgDC,QAAAA,IAAhD;AAAsDC,QAAAA;AAAtD,O,GAA2ET,U;;4CAOpEU,0B,WALZL,OAAO,CAAC,4BAAD,C,UACPG,IAAI,CAAC,sCAAD,C,UACJC,gBAAgB;AAAA;AAAA,6D,8CAChBH,gB,UACAC,iB,UAJD,MAKaG,0BALb,SAKgDR,SALhD,CAM6C;AAAA;AAAA;AAAA,eAC/BS,OAD+B;AAAA,eAE/BC,SAF+B;AAAA;;AAGzCC,QAAAA,cAAc,GAAW;AACrB,iBAAO,CAAP;AACH;;AACDC,QAAAA,cAAc,GAAW;AACrB,iBAAO,GAAP;AACH;;AACDC,QAAAA,QAAQ,GAAS;AACb,eAAKJ,OAAL,GAAe,KAAKK,YAAL;AAAA;AAAA,iEAAf;AACA,eAAKJ,SAAL,GAAiB,KAAKD,OAAL,CAAaM,mBAAb,EAAjB;;AAEA,cAAI,CAACC,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqC,KAAKT,SAA1C,EAAqD,SAArD,CAAL,EAAsE;AAClEM,YAAAA,MAAM,CAACI,cAAP,CAAsB,KAAKV,SAA3B,EAAsC,SAAtC,EAAiD;AAC7CW,cAAAA,KAAK,EAAE,EADsC;AAE7CC,cAAAA,YAAY,EAAE,KAF+B;AAG7CC,cAAAA,UAAU,EAAE,KAHiC;AAI7CC,cAAAA,QAAQ,EAAE;AAJmC,aAAjD;AAMH;;AAEDzB,UAAAA,MAAM,CAAC,KAAKW,SAAL,CAAee,OAAf,KAA2BC,SAA5B,CAAN;;AACA,eAAKhB,SAAL,CAAee,OAAf,CAAuBE,IAAvB,CAA4B,IAA5B;;AAEA,cAAIzB,MAAJ,EAAY;AACR,iBAAKO,OAAL,CAAamB,uBAAb;AACH;AACJ;;AACDC,QAAAA,SAAS,GAAS;AACd9B,UAAAA,MAAM,CAACiB,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqC,KAAKT,SAA1C,EAAqD,SAArD,CAAD,CAAN;AACA,cAAMoB,MAAM,GAAG,KAAKpB,SAAL,CAAee,OAA9B;AACA1B,UAAAA,MAAM,CAAC+B,MAAM,KAAKJ,SAAZ,CAAN;AACA,cAAMK,GAAG,GAAGD,MAAM,CAACE,OAAP,CAAe,IAAf,CAAZ;AACAjC,UAAAA,MAAM,CAACgC,GAAG,IAAI,CAAR,CAAN;AACAD,UAAAA,MAAM,CAACG,MAAP,CAAcF,GAAd,EAAmB,CAAnB;AACH;;AApCwC,O", "sourcesContent": ["/*\r\n Copyright (c) 2021-2024 Xiamen Yaji Software Co., Ltd.\r\n\r\n https://www.cocos.com/\r\n\r\n Permission is hereby granted, free of charge, to any person obtaining a copy\r\n of this software and associated documentation files (the \"Software\"), to deal\r\n in the Software without restriction, including without limitation the rights to\r\n use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\r\n of the Software, and to permit persons to whom the Software is furnished to do so,\r\n subject to the following conditions:\r\n\r\n The above copyright notice and this permission notice shall be included in\r\n all copies or substantial portions of the Software.\r\n\r\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\n THE SOFTWARE.\r\n*/\r\n\r\nimport {\r\n    _decorator,\r\n    assert,\r\n    Component,\r\n    rendering,\r\n} from 'cc';\r\n\r\nimport { BuiltinPipelineSettings } from './builtin-pipeline-settings';\r\nimport { PipelineSettings2 } from './builtin-pipeline';\r\nimport { EDITOR } from 'cc/env';\r\n\r\nconst { ccclass, disallowMultiple, executeInEditMode, menu, requireComponent } = _decorator;\r\n\r\n@ccclass('BuiltinPipelinePassBuilder')\r\n@menu('Rendering/BuiltinPipelinePassBuilder')\r\n@requireComponent(BuiltinPipelineSettings)\r\n@disallowMultiple\r\n@executeInEditMode\r\nexport class BuiltinPipelinePassBuilder extends Component\r\n    implements rendering.PipelinePassBuilder {\r\n    protected _parent!: BuiltinPipelineSettings;\r\n    protected _settings!: PipelineSettings2;\r\n    getConfigOrder(): number {\r\n        return 0;\r\n    }\r\n    getRenderOrder(): number {\r\n        return 200;\r\n    }\r\n    onEnable(): void {\r\n        this._parent = this.getComponent(BuiltinPipelineSettings)!;\r\n        this._settings = this._parent.getPipelineSettings();\r\n\r\n        if (!Object.prototype.hasOwnProperty.call(this._settings, '_passes')) {\r\n            Object.defineProperty(this._settings, '_passes', {\r\n                value: [],\r\n                configurable: false,\r\n                enumerable: false,\r\n                writable: true,\r\n            });\r\n        }\r\n\r\n        assert(this._settings._passes !== undefined);\r\n        this._settings._passes.push(this);\r\n\r\n        if (EDITOR) {\r\n            this._parent._tryEnableEditorPreview();\r\n        }\r\n    }\r\n    onDisable(): void {\r\n        assert(Object.prototype.hasOwnProperty.call(this._settings, '_passes'));\r\n        const passes = this._settings._passes;\r\n        assert(passes !== undefined);\r\n        const idx = passes.indexOf(this);\r\n        assert(idx >= 0);\r\n        passes.splice(idx, 1);\r\n    }\r\n}\r\n"]}